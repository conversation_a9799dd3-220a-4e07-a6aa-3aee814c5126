import { Link } from 'react-router-dom';
import {
  CreditCard,
  Settings as SettingsIcon,
  Info,
  LogOut,
  BarChart2,
  FileText,
  Shield
} from 'lucide-react';

export function UserMenu({ email, onSignOut }: { email: string, onSignOut: () => void }) {
  return (
    <div className="w-full max-w-sm bg-background border border-border/40 rounded-md shadow-sm">
      <div className="p-4 border-b border-border/40">
        <p className="text-sm text-muted-foreground truncate">{email}</p>
      </div>

      <div className="p-2">
        <div className="mb-2">
          <p className="px-2 text-xs font-medium text-muted-foreground mb-1">Account</p>
          <Link to="/subscription/manage" className="flex items-center gap-2 w-full p-2 text-sm rounded-md hover:bg-accent">
            <CreditCard className="h-4 w-4" />
            <span>Manage Subscription</span>
          </Link>

          <Link to="/settings" className="flex items-center gap-2 w-full p-2 text-sm rounded-md hover:bg-accent">
            <SettingsIcon className="h-4 w-4" />
            <span>Settings</span>
          </Link>

          <Link to="/about" className="flex items-center gap-2 w-full p-2 text-sm rounded-md hover:bg-accent">
            <Info className="h-4 w-4" />
            <span>About</span>
          </Link>
        </div>

        <div className="mb-2">
          <p className="px-2 text-xs font-medium text-muted-foreground mb-1">Investing</p>
          <Link to="/portfolio-builder" className="flex items-center gap-2 w-full p-2 text-sm rounded-md hover:bg-accent">
            <BarChart2 className="h-4 w-4" />
            <span>Portfolio Builder</span>
          </Link>
        </div>

        <div className="mb-2">
          <p className="px-2 text-xs font-medium text-muted-foreground mb-1">Legal</p>
          <Link to="/terms" className="flex items-center gap-2 w-full p-2 text-sm rounded-md hover:bg-accent">
            <FileText className="h-4 w-4" />
            <span>Terms of Service</span>
          </Link>
          <Link to="/privacy" className="flex items-center gap-2 w-full p-2 text-sm rounded-md hover:bg-accent">
            <Shield className="h-4 w-4" />
            <span>Privacy Policy</span>
          </Link>
        </div>

          <button
            onClick={onSignOut}
            className="flex items-center gap-2 w-full p-2 text-sm rounded-md hover:bg-accent"
          >
            <LogOut className="h-4 w-4" />
            <span>Sign Out</span>
          </button>
        </div>
      </div>
    </div>
  );
}