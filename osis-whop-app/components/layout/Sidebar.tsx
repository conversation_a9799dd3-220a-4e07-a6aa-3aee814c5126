import React from 'react';
import { NavLink } from 'react-router-dom';
import { Home, TrendingUp, Briefcase, Hammer, MessageCircle } from 'lucide-react'; // Lucide fallbacks

const navItems = [
  { name: 'Home', path: '/', icon: Home },
  { name: 'Stock Scanner', path: '/stock-scanner', icon: TrendingUp },
  { name: 'Portfolio Builder', path: '/portfolio-builder', icon: Briefcase },
  { name: 'Agent Builder', path: '/agent-builder', icon: Hammer },
  { name: 'Chat Interface', path: '/chat', icon: MessageCircle },
];

const Sidebar: React.FC = () => {
  return (
    <div className="w-64 bg-gray-800 text-white h-screen flex flex-col p-4">
      <div className="text-2xl font-bold mb-6">Syncraft</div> {/* Replace with logo later */}
      <nav className="flex flex-col space-y-2">
        {navItems.map((item) => (
          <NavLink
            key={item.name}
            to={item.path}
            className={({ isActive }) =>
              `flex items-center gap-3 px-3 py-2 rounded-md transition-colors duration-200 ${isActive ? 'bg-blue-600 text-white' : 'text-gray-300 hover:bg-gray-700'}`
            }
          >
            <item.icon className="h-5 w-5" />
            <span>{item.name}</span>
          </NavLink>
        ))}
      </nav>
      {/* Future: Add user info, credits, etc. */}
    </div>
  );
};

export default Sidebar;
