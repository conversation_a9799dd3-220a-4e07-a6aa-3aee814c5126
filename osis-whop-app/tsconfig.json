{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "allowJs": true, "strict": false, "noEmit": true, "esModuleInterop": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "moduleDetection": "force", "jsx": "preserve", "incremental": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitAny": false, "noFallthroughCasesInSwitch": false, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"]}, "strictNullChecks": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}