import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  CheckCircle,
  TrendingUp,
  Target,
  BarChart3,
  Zap,
  Activity,
  DollarSign,
  Bot
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useGamification } from '@/contexts/GamificationContext';
import { useWatchlist } from '@/contexts/WatchlistContext';

interface UserStats {
  agentsCreated: number;
  scansCompleted: number;
  backtestsCompleted: number;
  portfoliosCreated: number;
  winRate: number;
  totalTrades: number;
  successfulTrades: number;
  timeSavedHours: number;
  lastActivityDate: string | null;
}

interface UserAgent {
  id: string;
  name: string;
  description: string;
  returnPercentage: number;
  winRate: number;
  totalTrades: number;
  isActive: boolean;
  bestBacktest?: {
    returnPercentage: number;
    winRate: number;
    totalTrades: number;
    period: string;
  };
}

interface WatchlistItem {
  id: string;
  symbol: string;
  name: string;
  price: number;
  changePercent: number;
}



interface ProgressStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
  completed: boolean;
}

const Home: React.FC = () => {
  const navigate = useNavigate();
  const { userProgress } = useGamification();
  const { watchlist } = useWatchlist();

  // State for real user data
  const [userStats, setUserStats] = useState<UserStats>({
    agentsCreated: 0,
    scansCompleted: 0,
    backtestsCompleted: 0,
    portfoliosCreated: 0,
    winRate: 0,
    totalTrades: 0,
    successfulTrades: 0,
    timeSavedHours: 0,
    lastActivityDate: null
  });
  const [isLoading, setIsLoading] = useState(true);
  const [userName, setUserName] = useState('User');

  // New state for bento cards
  const [userAgents, setUserAgents] = useState<UserAgent[]>([]);

  // Enhanced mock data for premium dashboard
  const mockWatchlist: WatchlistItem[] = [
    { id: '1', symbol: 'AAPL', name: 'Apple Inc.', price: 185.42, changePercent: 2.1 },
    { id: '2', symbol: 'TSLA', name: 'Tesla Inc.', price: 248.50, changePercent: -1.8 },
    { id: '3', symbol: 'NVDA', name: 'NVIDIA Corp.', price: 875.30, changePercent: 4.2 },
    { id: '4', symbol: 'MSFT', name: 'Microsoft Corp.', price: 378.85, changePercent: 1.5 },
    { id: '5', symbol: 'GOOGL', name: 'Alphabet Inc.', price: 142.65, changePercent: -0.8 },
    { id: '6', symbol: 'META', name: 'Meta Platforms', price: 342.90, changePercent: 5.7 }
  ];

  const mockAgents: UserAgent[] = [
    {
      id: '1',
      name: 'Momentum Hunter',
      description: 'Identifies strong momentum stocks with technical analysis',
      returnPercentage: 24.5,
      winRate: 72,
      totalTrades: 45,
      isActive: true,
      bestBacktest: { returnPercentage: 24.5, winRate: 72, totalTrades: 45, period: '3M' }
    },
    {
      id: '2',
      name: 'Value Seeker',
      description: 'Finds undervalued opportunities using fundamental analysis',
      returnPercentage: 18.2,
      winRate: 68,
      totalTrades: 32,
      isActive: false,
      bestBacktest: { returnPercentage: 18.2, winRate: 68, totalTrades: 32, period: '6M' }
    },
    {
      id: '3',
      name: 'Breakout Trader',
      description: 'Captures breakout patterns with volume confirmation',
      returnPercentage: 31.8,
      winRate: 65,
      totalTrades: 28,
      isActive: true,
      bestBacktest: { returnPercentage: 31.8, winRate: 65, totalTrades: 28, period: '2M' }
    }
  ];

  const mockEarnings = {
    totalEarnings: 12847.50,
    salesCount: 23,
    monthlyGrowth: 18.4,
    topPerformer: 'Momentum Hunter'
  };

  const [checkedItems, setCheckedItems] = useState<{[key: string]: boolean}>({
    'create-agent': false,
    'first-scan': false,
    'first-backtest': false,
    'portfolio-setup': false,
    'discover-agents': false,
    'make-agent-public': false,
    'setup-marketplace': false
  });

  // Load user data on component mount
  useEffect(() => {
    loadUserData();
  }, []);

  // Reload user data when user progress changes (with stable dependencies)
  const progressDeps = useMemo(() => [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ], [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ]);

  useEffect(() => {
    loadUserData();
  }, progressDeps);

  // Update checked items based on real progress
  useEffect(() => {
    setCheckedItems({
      'create-agent': userStats.agentsCreated > 0,
      'first-scan': userProgress.scansCompleted > 0,
      'first-backtest': userProgress.backtestsCompleted > 0,
      'portfolio-setup': userProgress.portfoliosCreated > 0,
      'discover-agents': userProgress.hasVisitedDiscoverPage,
      'make-agent-public': userProgress.hasCreatedFirstPublicAgent,
      'setup-marketplace': false // This will be updated when user sets up marketplace
    });
  }, [userStats, userProgress]);

  const loadUserData = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Load agent statistics
      const { data: agents } = await supabase
        .from('agents')
        .select('id, created_at')
        .eq('user_id', user.id);

      // Calculate time saved
      const portfolioCount = userProgress.portfoliosCreated;
      const scanTimeMinutes = userProgress.stocksScanned * 1;
      const backtestTimeMinutes = userProgress.backtestsCompleted * 60;
      const portfolioTimeMinutes = portfolioCount * 30;

      const totalTimeMinutes = scanTimeMinutes + backtestTimeMinutes + portfolioTimeMinutes;
      const timeSavedHours = Math.round(totalTimeMinutes / 60 * 10) / 10;

      // Get last activity date from agents
      const lastActivityDate = agents?.length > 0
        ? new Date(Math.max(...agents.map((a: any) => new Date(a.created_at).getTime()))).toISOString()
        : null;

      setUserStats({
        agentsCreated: agents?.length || 0,
        scansCompleted: userProgress.scansCompleted,
        backtestsCompleted: userProgress.backtestsCompleted,
        portfoliosCreated: userProgress.portfoliosCreated,
        winRate: 0,
        totalTrades: userProgress.tradesExecuted,
        successfulTrades: 0,
        timeSavedHours,
        lastActivityDate
      });
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Progress steps for get started journey
  const progressSteps: ProgressStep[] = [
    {
      id: 'create-agent',
      title: 'Create Agent',
      description: 'Build your first AI trading agent',
      icon: <Zap className="w-4 h-4" />,
      action: () => navigate('/agent-builder'),
      completed: checkedItems['create-agent']
    },
    {
      id: 'first-scan',
      title: 'Run Scan',
      description: 'Discover trading opportunities',
      icon: <Target className="w-4 h-4" />,
      action: () => navigate('/scanner'),
      completed: checkedItems['first-scan']
    },
    {
      id: 'first-backtest',
      title: 'Backtest Strategy',
      description: 'Test against historical data',
      icon: <BarChart3 className="w-4 h-4" />,
      action: () => navigate('/backtesting'),
      completed: checkedItems['first-backtest']
    },
    {
      id: 'discover-agents',
      title: 'Explore Marketplace',
      description: 'Discover community agents',
      icon: <Activity className="w-4 h-4" />,
      action: () => navigate('/marketplace'),
      completed: checkedItems['discover-agents']
    }
  ];

  const completedSteps = progressSteps.filter(step => step.completed).length;

  // Load user name
  useEffect(() => {
    const loadUserName = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user?.user_metadata?.full_name) {
          const firstName = user.user_metadata.full_name.split(' ')[0];
          setUserName(firstName);
        }
      } catch (error) {
        console.error('Error loading user name:', error);
      }
    };
    loadUserName();
  }, []);

  // Load mock user agents data
  useEffect(() => {
    const mockUserAgents: UserAgent[] = [
      {
        id: '1',
        name: 'Momentum Trader',
        description: 'Identifies stocks with strong momentum patterns',
        returnPercentage: 24.5,
        winRate: 68,
        totalTrades: 45,
        isActive: true,
        bestBacktest: {
          returnPercentage: 24.5,
          winRate: 68,
          totalTrades: 45,
          period: '3M'
        }
      },
      {
        id: '2',
        name: 'Value Hunter',
        description: 'Finds undervalued stocks with strong fundamentals',
        returnPercentage: 18.2,
        winRate: 72,
        totalTrades: 32,
        isActive: false,
        bestBacktest: {
          returnPercentage: 18.2,
          winRate: 72,
          totalTrades: 32,
          period: '6M'
        }
      }
    ];
    setUserAgents(mockUserAgents);
  }, []);

  if (isLoading) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
          <p className="text-white/60">Loading your headquarters...</p>
        </div>
      </div>
    );
  }





  // Generate realistic price data for charts
  const generatePriceData = (symbol: string, currentPrice: number, changePercent: number) => {
    const points = 24; // 24 hours of data
    const data = [];
    const startPrice = currentPrice / (1 + changePercent / 100);

    for (let i = 0; i < points; i++) {
      const progress = i / (points - 1);
      const trend = changePercent / 100 * progress;
      const noise = (Math.sin(i * 0.5) + Math.cos(i * 0.3)) * 0.02;
      const volatility = Math.random() * 0.01 - 0.005;
      const price = startPrice * (1 + trend + noise + volatility);
      data.push(price);
    }
    return data;
  };

  // Enhanced line chart component for watchlist items
  const renderMiniChart = (symbol: string, currentPrice: number, changePercent: number) => {
    const data = generatePriceData(symbol, currentPrice, changePercent);
    const minPrice = Math.min(...data);
    const maxPrice = Math.max(...data);
    const priceRange = maxPrice - minPrice || 1;

    const points = data.map((price, i) => {
      const x = (i / (data.length - 1)) * 40;
      const y = 20 - ((price - minPrice) / priceRange) * 16;
      return `${x},${y}`;
    }).join(' ');

    const isPositive = changePercent >= 0;
    const strokeColor = isPositive ? '#10b981' : '#ef4444';
    const fillColor = isPositive ? 'rgba(16, 185, 129, 0.1)' : 'rgba(239, 68, 68, 0.1)';

    return (
      <svg width="42" height="24" className="opacity-80">
        <defs>
          <linearGradient id={`gradient-${symbol}`} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={strokeColor} stopOpacity="0.3"/>
            <stop offset="100%" stopColor={strokeColor} stopOpacity="0"/>
          </linearGradient>
        </defs>
        <path
          d={`M${points.split(' ')[0]} L${points} L40,20 L0,20 Z`}
          fill={`url(#gradient-${symbol})`}
        />
        <polyline
          fill="none"
          stroke={strokeColor}
          strokeWidth="1.5"
          points={points}
        />
      </svg>
    );
  };





  // Premium Earnings Card for Bento Layout
  const renderPremiumEarningsCard = () => (
    <div className="relative h-full flex flex-col">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="w-14 h-14 bg-gradient-to-br from-green-500 to-green-400 rounded-2xl flex items-center justify-center shadow-lg shadow-green-500/30">
            <DollarSign className="w-7 h-7 text-white" />
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white">Earnings</h3>
            <p className="text-white/60">Total marketplace revenue</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-3xl font-bold text-green-300">${mockEarnings.totalEarnings.toLocaleString()}</div>
          <div className="text-green-400 text-sm font-medium">+{mockEarnings.monthlyGrowth}% this month</div>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-6 mb-6">
        <div className="text-center p-4 bg-white/[0.02] rounded-xl border border-white/[0.05]">
          <div className="text-2xl font-bold text-white">{mockEarnings.salesCount}</div>
          <div className="text-white/60 text-sm">Sales</div>
        </div>
        <div className="text-center p-4 bg-white/[0.02] rounded-xl border border-white/[0.05]">
          <div className="text-2xl font-bold text-green-300">#{1}</div>
          <div className="text-white/60 text-sm">Rank</div>
        </div>
        <div className="text-center p-4 bg-white/[0.02] rounded-xl border border-white/[0.05]">
          <div className="text-2xl font-bold text-white">4.9</div>
          <div className="text-white/60 text-sm">Rating</div>
        </div>
      </div>

      <div className="mt-auto">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => navigate('/marketplace')}
          className="w-full bg-gradient-to-r from-green-500 to-green-400 text-white font-semibold py-3 px-6 rounded-xl shadow-lg shadow-green-500/30 hover:shadow-xl hover:shadow-green-500/40 transition-all duration-300"
        >
          View Marketplace
        </motion.button>
      </div>
    </div>
  );

  // Premium Watchlist Card for Bento Layout
  const renderPremiumWatchlistCard = () => (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-white/[0.08] to-white/[0.04] rounded-xl flex items-center justify-center border border-white/[0.10]">
            <TrendingUp className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">Watchlist</h3>
            <p className="text-white/50 text-sm">Your tracked stocks</p>
          </div>
        </div>
      </div>

      <div className="flex-1 space-y-3 overflow-y-auto">
        {mockWatchlist.slice(0, 6).map((stock) => (
          <motion.div
            key={stock.id}
            whileHover={{ scale: 1.02 }}
            onClick={() => navigate(`/stock-screener/${stock.symbol}`)}
            className="p-4 bg-white/[0.02] rounded-xl border border-white/[0.05] hover:border-white/[0.10] cursor-pointer transition-all duration-200 group"
          >
            <div className="flex items-center justify-between mb-3">
              <div>
                <div className="font-semibold text-white text-sm">{stock.symbol}</div>
                <div className="text-white/60 text-xs">{stock.name}</div>
              </div>
              <div className="text-right">
                <div className="text-white font-medium text-sm">${stock.price}</div>
                <div className={`text-xs font-medium ${
                  stock.changePercent >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {stock.changePercent >= 0 ? '+' : ''}{stock.changePercent}%
                </div>
              </div>
            </div>

            {/* Mini Sparkline Chart */}
            <div className="h-8 w-full">
              <svg width="100%" height="100%" viewBox="0 0 100 20" className="opacity-70">
                <defs>
                  <linearGradient id={`gradient-${stock.id}`} x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" stopColor={stock.changePercent >= 0 ? "#10b981" : "#ef4444"} stopOpacity="0.3"/>
                    <stop offset="100%" stopColor={stock.changePercent >= 0 ? "#10b981" : "#ef4444"} stopOpacity="0"/>
                  </linearGradient>
                </defs>
                <path
                  d={`M0,${stock.changePercent >= 0 ? '15' : '5'} Q25,${stock.changePercent >= 0 ? '10' : '10'} 50,${stock.changePercent >= 0 ? '8' : '12'} T100,${stock.changePercent >= 0 ? '5' : '15'} L100,20 L0,20 Z`}
                  fill={`url(#gradient-${stock.id})`}
                />
                <path
                  d={`M0,${stock.changePercent >= 0 ? '15' : '5'} Q25,${stock.changePercent >= 0 ? '10' : '10'} 50,${stock.changePercent >= 0 ? '8' : '12'} T100,${stock.changePercent >= 0 ? '5' : '15'}`}
                  fill="none"
                  stroke={stock.changePercent >= 0 ? "#10b981" : "#ef4444"}
                  strokeWidth="1.5"
                />
              </svg>
            </div>
          </motion.div>
        ))}
      </div>

      {mockWatchlist.length === 0 && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <TrendingUp className="w-12 h-12 text-white/30 mx-auto mb-3" />
            <p className="text-white/60 text-sm mb-4">No stocks in watchlist</p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              onClick={() => navigate('/stock-screener')}
              className="bg-gradient-to-r from-green-500 to-green-400 text-white text-sm font-medium py-2 px-4 rounded-lg"
            >
              Add Stocks
            </motion.button>
          </div>
        </div>
      )}
    </div>
  );

  // Premium Agents Card for Bento Layout
  const renderPremiumAgentsCard = () => (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-white/[0.08] to-white/[0.04] rounded-xl flex items-center justify-center border border-white/[0.10]">
            <Bot className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">Your Agents</h3>
            <p className="text-white/50 text-sm">Trading strategies</p>
          </div>
        </div>
        <motion.button
          whileHover={{ scale: 1.05 }}
          onClick={() => navigate('/builder')}
          className="bg-gradient-to-r from-green-500 to-green-400 text-white text-xs font-medium py-2 px-3 rounded-lg shadow-lg"
        >
          Create New
        </motion.button>
      </div>

      <div className="flex-1 space-y-3">
        {mockAgents.slice(0, 3).map((agent) => (
          <motion.div
            key={agent.id}
            whileHover={{ scale: 1.02 }}
            className="p-4 bg-white/[0.02] rounded-xl border border-white/[0.05] hover:border-white/[0.10] cursor-pointer transition-all duration-200"
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                  agent.isActive
                    ? 'bg-gradient-to-r from-green-500 to-green-400'
                    : 'bg-white/[0.08]'
                }`}>
                  <Bot className="w-4 h-4 text-white" />
                </div>
                <div>
                  <div className="font-medium text-white text-sm">{agent.name}</div>
                  <div className="text-white/60 text-xs">{agent.description}</div>
                </div>
              </div>
              <div className={`w-2 h-2 rounded-full ${
                agent.isActive ? 'bg-green-400' : 'bg-white/30'
              }`} />
            </div>

            <div className="grid grid-cols-3 gap-2 text-center">
              <div>
                <div className={`text-sm font-semibold ${
                  agent.returnPercentage >= 20 ? 'text-green-300' :
                  agent.returnPercentage >= 10 ? 'text-green-400' :
                  agent.returnPercentage >= 0 ? 'text-green-500' : 'text-red-400'
                }`}>
                  {agent.returnPercentage >= 0 ? '+' : ''}{agent.returnPercentage}%
                </div>
                <div className="text-white/50 text-xs">Return</div>
              </div>
              <div>
                <div className="text-sm font-semibold text-white">{agent.winRate}%</div>
                <div className="text-white/50 text-xs">Win Rate</div>
              </div>
              <div>
                <div className="text-sm font-semibold text-white">{agent.totalTrades}</div>
                <div className="text-white/50 text-xs">Trades</div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {mockAgents.length === 0 && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Bot className="w-12 h-12 text-white/30 mx-auto mb-3" />
            <p className="text-white/60 text-sm mb-4">No agents created yet</p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              onClick={() => navigate('/builder')}
              className="bg-gradient-to-r from-green-500 to-green-400 text-white text-sm font-medium py-2 px-4 rounded-lg"
            >
              Create Agent
            </motion.button>
          </div>
        </div>
      )}
    </div>
  );

  // Premium Performance Card for Bento Layout
  const renderPremiumPerformanceCard = () => (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-white/[0.08] to-white/[0.04] rounded-xl flex items-center justify-center border border-white/[0.10]">
            <BarChart3 className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">Portfolio Performance</h3>
            <p className="text-white/50 text-sm">Last 30 days</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-xl font-bold text-green-300">+12.4%</div>
          <div className="text-white/50 text-xs">Total Return</div>
        </div>
      </div>

      <div className="flex-1 flex items-center justify-center mb-4">
        <div className="w-full h-20 bg-white/[0.02] rounded-xl border border-white/[0.05] p-4 flex items-center justify-center">
          <svg width="100%" height="100%" viewBox="0 0 200 40" className="opacity-80">
            <defs>
              <linearGradient id="performanceGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="#10b981" stopOpacity="0.3"/>
                <stop offset="100%" stopColor="#10b981" stopOpacity="0"/>
              </linearGradient>
            </defs>
            <path
              d="M0,30 Q50,25 100,15 T200,10 L200,40 L0,40 Z"
              fill="url(#performanceGradient)"
            />
            <path
              d="M0,30 Q50,25 100,15 T200,10"
              fill="none"
              stroke="#10b981"
              strokeWidth="2"
            />
          </svg>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-3">
        <div className="text-center p-3 bg-white/[0.02] rounded-lg border border-white/[0.05]">
          <div className="text-lg font-bold text-green-300">68%</div>
          <div className="text-white/50 text-xs">Win Rate</div>
        </div>
        <div className="text-center p-3 bg-white/[0.02] rounded-lg border border-white/[0.05]">
          <div className="text-lg font-bold text-white">156</div>
          <div className="text-white/50 text-xs">Trades</div>
        </div>
        <div className="text-center p-3 bg-white/[0.02] rounded-lg border border-white/[0.05]">
          <div className="text-lg font-bold text-green-300">2.1</div>
          <div className="text-white/50 text-xs">Sharpe</div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      {/* Premium Header */}
      <div className="px-8 py-8 border-b border-white/[0.08] bg-gradient-to-br from-[#0A0A0A] via-[#0F0F0F] to-[#0A0A0A] relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 via-transparent to-green-400/5"></div>
        <div className="relative z-10">
          <h1 className="text-3xl font-light text-white mb-2">
            Welcome to HQ, <span className="font-medium bg-gradient-to-r from-green-300 to-green-400 bg-clip-text text-transparent">{userName}</span>
          </h1>
          <p className="text-white/60 text-lg">Your premium trading command center</p>
        </div>
      </div>

      {/* Premium Bento Dashboard */}
      <div className="p-8 space-y-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Get Started Progress - Full Width Stepper */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-br from-white/[0.03] to-white/[0.01] border border-white/[0.08] rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <div className="flex items-center justify-between mb-8">
              <div>
                <h3 className="text-2xl font-semibold text-white mb-2">Get Started Journey</h3>
                <p className="text-white/60">Complete these steps to unlock your trading potential</p>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold bg-gradient-to-r from-green-400 to-green-300 bg-clip-text text-transparent">
                  {Math.round((completedSteps / progressSteps.length) * 100)}%
                </div>
                <div className="text-white/50 text-sm">Complete</div>
              </div>
            </div>

            {/* Premium Progress Bar */}
            <div className="w-full h-3 bg-gradient-to-r from-white/[0.05] to-white/[0.08] rounded-full overflow-hidden mb-8 shadow-inner">
              <motion.div
                className="h-full bg-gradient-to-r from-green-500 via-green-400 to-green-300 rounded-full shadow-lg shadow-green-500/30"
                initial={{ width: 0 }}
                animate={{ width: `${(completedSteps / progressSteps.length) * 100}%` }}
                transition={{ duration: 1.5, ease: "easeOut" }}
              />
            </div>

            {/* Premium Progress Steps */}
            <div className="grid grid-cols-4 gap-6">
              {progressSteps.map((step, index) => (
                <motion.div
                  key={step.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  onClick={step.action}
                  className={`p-6 rounded-2xl border cursor-pointer transition-all duration-300 ${
                    step.completed
                      ? 'bg-gradient-to-br from-green-500/15 to-green-400/8 border-green-500/30 shadow-lg shadow-green-500/20'
                      : 'bg-white/[0.02] border-white/[0.08] hover:border-white/[0.15] hover:bg-white/[0.04] hover:shadow-lg'
                  }`}
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className={`w-12 h-12 rounded-2xl flex items-center justify-center transition-all duration-300 ${
                      step.completed
                        ? 'bg-gradient-to-r from-green-500 to-green-400 shadow-lg shadow-green-500/30'
                        : 'bg-white/[0.08] border border-white/[0.15]'
                    }`}>
                      {step.completed ? (
                        <CheckCircle className="w-6 h-6 text-white" />
                      ) : (
                        <div className="w-3 h-3 bg-white/50 rounded-full" />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className={`font-semibold text-base ${step.completed ? 'text-green-300' : 'text-white'}`}>
                        {step.title}
                      </h4>
                    </div>
                  </div>
                  <p className="text-white/60 text-sm leading-relaxed">{step.description}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Premium Asymmetric Bento Grid */}
          <div className="grid grid-cols-6 gap-6 h-[500px]">
            {/* Earnings - Large Card (col-span-4) */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              whileHover={{ scale: 1.02 }}
              className="col-span-4 bg-gradient-to-br from-green-500/10 to-green-400/5 border border-green-500/20 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group relative overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              {renderPremiumEarningsCard()}
            </motion.div>

            {/* Watchlist - Tall Card (col-span-2 row-span-2) */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              whileHover={{ scale: 1.02 }}
              className="col-span-2 row-span-2 bg-gradient-to-br from-white/[0.03] to-white/[0.01] border border-white/[0.08] rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {renderPremiumWatchlistCard()}
            </motion.div>

            {/* Your Agents - Medium Wide (col-span-3) */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              whileHover={{ scale: 1.02 }}
              className="col-span-3 bg-gradient-to-br from-white/[0.03] to-white/[0.01] border border-white/[0.08] rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {renderPremiumAgentsCard()}
            </motion.div>

            {/* Portfolio Performance - Medium Wide (col-span-3) */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              whileHover={{ scale: 1.02 }}
              className="col-span-3 bg-gradient-to-br from-white/[0.03] to-white/[0.01] border border-white/[0.08] rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {renderPremiumPerformanceCard()}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
