import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  CheckCircle,
  TrendingUp,
  Target,
  BarChart3,
  Zap,
  Activity,
  DollarSign,
  Bot
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useGamification } from '@/contexts/GamificationContext';
import { useWatchlist } from '@/contexts/WatchlistContext';

interface UserStats {
  agentsCreated: number;
  scansCompleted: number;
  backtestsCompleted: number;
  portfoliosCreated: number;
  winRate: number;
  totalTrades: number;
  successfulTrades: number;
  timeSavedHours: number;
  lastActivityDate: string | null;
}

interface UserAgent {
  id: string;
  name: string;
  description: string;
  bestBacktest?: {
    totalReturn: number;
    winRate: number;
    totalTrades: number;
  };
}



interface ProgressStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
  completed: boolean;
}

const Home: React.FC = () => {
  const navigate = useNavigate();
  const { userProgress } = useGamification();
  const { watchlist } = useWatchlist();

  // State for real user data
  const [userStats, setUserStats] = useState<UserStats>({
    agentsCreated: 0,
    scansCompleted: 0,
    backtestsCompleted: 0,
    portfoliosCreated: 0,
    winRate: 0,
    totalTrades: 0,
    successfulTrades: 0,
    timeSavedHours: 0,
    lastActivityDate: null
  });
  const [isLoading, setIsLoading] = useState(true);
  const [userName, setUserName] = useState('User');

  // New state for bento cards
  const [userAgents, setUserAgents] = useState<UserAgent[]>([]);

  const [checkedItems, setCheckedItems] = useState<{[key: string]: boolean}>({
    'create-agent': false,
    'first-scan': false,
    'first-backtest': false,
    'portfolio-setup': false,
    'discover-agents': false,
    'make-agent-public': false,
    'setup-marketplace': false
  });

  // Load user data on component mount
  useEffect(() => {
    loadUserData();
  }, []);

  // Reload user data when user progress changes (with stable dependencies)
  const progressDeps = useMemo(() => [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ], [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ]);

  useEffect(() => {
    loadUserData();
  }, progressDeps);

  // Update checked items based on real progress
  useEffect(() => {
    setCheckedItems({
      'create-agent': userStats.agentsCreated > 0,
      'first-scan': userProgress.scansCompleted > 0,
      'first-backtest': userProgress.backtestsCompleted > 0,
      'portfolio-setup': userProgress.portfoliosCreated > 0,
      'discover-agents': userProgress.hasVisitedDiscoverPage,
      'make-agent-public': userProgress.hasCreatedFirstPublicAgent,
      'setup-marketplace': false // This will be updated when user sets up marketplace
    });
  }, [userStats, userProgress]);

  const loadUserData = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Load agent statistics
      const { data: agents } = await supabase
        .from('agents')
        .select('id, created_at')
        .eq('user_id', user.id);

      // Calculate time saved
      const portfolioCount = userProgress.portfoliosCreated;
      const scanTimeMinutes = userProgress.stocksScanned * 1;
      const backtestTimeMinutes = userProgress.backtestsCompleted * 60;
      const portfolioTimeMinutes = portfolioCount * 30;

      const totalTimeMinutes = scanTimeMinutes + backtestTimeMinutes + portfolioTimeMinutes;
      const timeSavedHours = Math.round(totalTimeMinutes / 60 * 10) / 10;

      // Get last activity date from agents
      const lastActivityDate = agents?.length > 0
        ? new Date(Math.max(...agents.map((a: any) => new Date(a.created_at).getTime()))).toISOString()
        : null;

      setUserStats({
        agentsCreated: agents?.length || 0,
        scansCompleted: userProgress.scansCompleted,
        backtestsCompleted: userProgress.backtestsCompleted,
        portfoliosCreated: userProgress.portfoliosCreated,
        winRate: 0,
        totalTrades: userProgress.tradesExecuted,
        successfulTrades: 0,
        timeSavedHours,
        lastActivityDate
      });
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Progress steps for get started journey
  const progressSteps: ProgressStep[] = [
    {
      id: 'create-agent',
      title: 'Create Agent',
      description: 'Build your first AI trading agent',
      icon: <Zap className="w-4 h-4" />,
      action: () => navigate('/agent-builder'),
      completed: checkedItems['create-agent']
    },
    {
      id: 'first-scan',
      title: 'Run Scan',
      description: 'Discover trading opportunities',
      icon: <Target className="w-4 h-4" />,
      action: () => navigate('/scanner'),
      completed: checkedItems['first-scan']
    },
    {
      id: 'first-backtest',
      title: 'Backtest Strategy',
      description: 'Test against historical data',
      icon: <BarChart3 className="w-4 h-4" />,
      action: () => navigate('/backtesting'),
      completed: checkedItems['first-backtest']
    },
    {
      id: 'discover-agents',
      title: 'Explore Marketplace',
      description: 'Discover community agents',
      icon: <Activity className="w-4 h-4" />,
      action: () => navigate('/marketplace'),
      completed: checkedItems['discover-agents']
    }
  ];

  const completedSteps = progressSteps.filter(step => step.completed).length;

  // Load user name
  useEffect(() => {
    const loadUserName = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user?.user_metadata?.full_name) {
          const firstName = user.user_metadata.full_name.split(' ')[0];
          setUserName(firstName);
        }
      } catch (error) {
        console.error('Error loading user name:', error);
      }
    };
    loadUserName();
  }, []);

  // Load mock user agents data
  useEffect(() => {
    const mockAgents: UserAgent[] = [
      {
        id: '1',
        name: 'Momentum Trader',
        description: 'Identifies stocks with strong momentum patterns',
        bestBacktest: {
          totalReturn: 24.5,
          winRate: 68,
          totalTrades: 45
        }
      },
      {
        id: '2',
        name: 'Value Hunter',
        description: 'Finds undervalued stocks with strong fundamentals',
        bestBacktest: {
          totalReturn: 18.2,
          winRate: 72,
          totalTrades: 32
        }
      }
    ];
    setUserAgents(mockAgents);
  }, []);

  if (isLoading) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
          <p className="text-white/60">Loading your headquarters...</p>
        </div>
      </div>
    );
  }

  // Render function for Agents Card
  const renderAgentsCard = () => (
    <div className="h-full bg-gradient-to-br from-[#1A1A1A] to-[#151515] border border-white/[0.08] rounded-xl p-6 shadow-2xl shadow-black/40 relative overflow-hidden hover:border-green-500/20 transition-all duration-300">
      {/* Subtle top accent line */}
      <div className="absolute top-0 left-6 right-6 h-px bg-gradient-to-r from-transparent via-green-500/30 to-transparent" />

      <div className="absolute inset-0 rounded-xl shadow-inner shadow-black/30 pointer-events-none"></div>
      <div className="relative z-10 h-full flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-500/20 to-green-400/10 rounded-xl flex items-center justify-center shadow-lg border border-green-500/20">
              <Bot className="w-5 h-5 text-green-400" />
            </div>
            <div>
              <h3 className="text-lg font-medium bg-gradient-to-r from-white to-white/90 bg-clip-text text-transparent">Your Agents</h3>
              <p className="text-white/50 text-xs">AI trading strategies</p>
            </div>
          </div>
          <button
            onClick={() => navigate('/agent-builder')}
            className="px-4 py-2 bg-gradient-to-r from-green-500/10 to-green-400/5 border border-green-500/20 rounded-lg text-green-300 hover:text-green-200 hover:border-green-400/30 text-sm transition-all duration-200 shadow-inner shadow-black/20"
          >
            Create New
          </button>
        </div>

        <div className="flex-1">
          {userAgents.length === 0 ? (
            <div className="text-center py-8">
              <div className="w-12 h-12 bg-white/[0.02] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-inner shadow-black/20">
                <Bot className="w-6 h-6 text-white/30" />
              </div>
              <p className="text-white/50 text-sm mb-4">No agents created yet</p>
              <button
                onClick={() => navigate('/agent-builder')}
                className="px-4 py-2 bg-gradient-to-r from-white/[0.08] to-white/[0.04] border border-white/[0.12] rounded-lg text-white/80 hover:text-white text-sm transition-all duration-200 shadow-inner shadow-black/20"
              >
                Create Your First Agent
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {userAgents.slice(0, 4).map((agent) => (
                <div key={agent.id} className="p-4 bg-gradient-to-br from-white/[0.03] to-white/[0.01] rounded-lg hover:from-white/[0.05] hover:to-white/[0.02] transition-all duration-200 shadow-inner shadow-black/10 cursor-pointer border border-white/[0.05] hover:border-white/[0.08] relative overflow-hidden group">
                  {/* Subtle corner glow for high-performing agents */}
                  {agent.bestBacktest && agent.bestBacktest.totalReturn > 20 && (
                    <div className="absolute top-0 right-0 w-6 h-6 bg-gradient-to-bl from-green-400/30 to-transparent rounded-bl-lg rounded-tr-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  )}

                  <div className="relative z-10">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-white text-sm mb-1 truncate">{agent.name}</h4>
                        <p className="text-white/60 text-xs line-clamp-2">{agent.description}</p>
                      </div>
                    </div>
                    {agent.bestBacktest && (
                      <div className="grid grid-cols-3 gap-3 pt-3 border-t border-white/[0.08]">
                        <div className="text-center">
                          <div className={`font-semibold text-sm ${
                            agent.bestBacktest.totalReturn >= 20 ? 'text-green-300' :
                            agent.bestBacktest.totalReturn >= 10 ? 'text-green-400' :
                            agent.bestBacktest.totalReturn >= 0 ? 'text-green-500' : 'text-red-400'
                          }`}>
                            {agent.bestBacktest.totalReturn >= 0 ? '+' : ''}{agent.bestBacktest.totalReturn}%
                          </div>
                          <div className="text-white/50 text-xs">Return</div>
                        </div>
                        <div className="text-center">
                          <div className={`font-semibold text-sm ${
                            agent.bestBacktest.winRate >= 70 ? 'text-green-300' :
                            agent.bestBacktest.winRate >= 60 ? 'text-green-400' : 'text-white'
                          }`}>{agent.bestBacktest.winRate}%</div>
                          <div className="text-white/50 text-xs">Win Rate</div>
                        </div>
                        <div className="text-center">
                          <div className="text-white font-semibold text-sm">{agent.bestBacktest.totalTrades}</div>
                          <div className="text-white/50 text-xs">Trades</div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  // Render function for Earnings Card
  const renderEarningsCard = () => (
    <div className="h-full bg-gradient-to-br from-[#1A1A1A] to-[#151515] border border-white/[0.08] rounded-xl p-6 shadow-2xl shadow-black/40 relative overflow-hidden hover:border-green-500/20 transition-all duration-300">
      {/* Subtle side accent */}
      <div className="absolute left-0 top-6 bottom-6 w-px bg-gradient-to-b from-transparent via-green-500/30 to-transparent" />

      <div className="absolute inset-0 rounded-xl shadow-inner shadow-black/30 pointer-events-none"></div>
      <div className="relative z-10 h-full flex flex-col">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-gradient-to-br from-green-500/20 to-green-400/10 rounded-xl flex items-center justify-center shadow-lg border border-green-500/20">
            <DollarSign className="w-5 h-5 text-green-400" />
          </div>
          <div>
            <h3 className="text-lg font-medium bg-gradient-to-r from-white to-white/90 bg-clip-text text-transparent">Earnings</h3>
            <p className="text-white/50 text-xs">Revenue & sales</p>
          </div>
        </div>

        <div className="flex-1 space-y-4">
          <div className="p-4 bg-gradient-to-br from-white/[0.03] to-white/[0.01] rounded-lg shadow-inner shadow-black/10 border border-white/[0.05]">
            <div className="text-center">
              <div className="text-2xl font-bold bg-gradient-to-r from-green-300 to-green-400 bg-clip-text text-transparent mb-1">$0.00</div>
              <div className="text-white/60 text-sm">Total Earnings</div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <div className="p-3 bg-gradient-to-br from-white/[0.03] to-white/[0.01] rounded-lg shadow-inner shadow-black/10 text-center border border-white/[0.05]">
              <div className="text-lg font-semibold text-green-300">$0.00</div>
              <div className="text-white/50 text-xs">Available</div>
            </div>
            <div className="p-3 bg-gradient-to-br from-white/[0.03] to-white/[0.01] rounded-lg shadow-inner shadow-black/10 text-center border border-white/[0.05]">
              <div className="text-lg font-semibold text-white">0</div>
              <div className="text-white/50 text-xs">Sales</div>
            </div>
          </div>

          <div className="text-center pt-2">
            <p className="text-white/60 text-xs">Start selling agents to earn</p>
            <button
              onClick={() => navigate('/marketplace')}
              className="mt-2 px-3 py-1 bg-gradient-to-r from-green-500/10 to-green-400/5 border border-green-500/20 rounded-lg text-green-300 hover:text-green-200 hover:border-green-400/30 text-xs transition-all duration-200"
            >
              View Marketplace
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // Simple line chart component for watchlist items
  const renderMiniChart = (changePercent: number) => {
    const isPositive = changePercent >= 0;
    const points = Array.from({ length: 8 }, (_, i) => {
      const baseY = 20;
      const variation = Math.sin(i * 0.8) * 8 + (isPositive ? -5 : 5);
      return `${i * 6},${baseY + variation}`;
    }).join(' ');

    return (
      <svg width="42" height="24" className="opacity-60">
        <polyline
          fill="none"
          stroke={isPositive ? '#10b981' : '#ef4444'}
          strokeWidth="1.5"
          points={points}
        />
      </svg>
    );
  };

  // Render function for Watchlist Card
  const renderWatchlistCard = () => (
    <div className="h-full bg-gradient-to-br from-[#1A1A1A] to-[#151515] border border-white/[0.08] rounded-xl p-6 shadow-2xl shadow-black/40 relative overflow-hidden hover:border-green-500/20 transition-all duration-300">
      {/* Subtle bottom accent line */}
      <div className="absolute bottom-0 left-6 right-6 h-px bg-gradient-to-r from-transparent via-green-500/30 to-transparent" />

      <div className="absolute inset-0 rounded-xl shadow-inner shadow-black/30 pointer-events-none"></div>
      <div className="relative z-10 h-full flex flex-col">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-500/20 to-green-400/10 rounded-xl flex items-center justify-center shadow-lg border border-green-500/20">
              <TrendingUp className="w-5 h-5 text-green-400" />
            </div>
            <div>
              <h3 className="text-lg font-medium bg-gradient-to-r from-white to-white/90 bg-clip-text text-transparent">Watchlist</h3>
              <p className="text-white/50 text-xs">Starred stocks</p>
            </div>
          </div>
          <button
            onClick={() => navigate('/stock-screener')}
            className="px-3 py-1.5 bg-gradient-to-r from-green-500/10 to-green-400/5 border border-green-500/20 rounded-lg text-green-300 hover:text-green-200 hover:border-green-400/30 text-xs transition-all duration-200 shadow-inner shadow-black/20"
          >
            View All
          </button>
        </div>

        <div className="flex-1">
          {watchlist.length === 0 ? (
            <div className="text-center py-8">
              <div className="w-12 h-12 bg-white/[0.02] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-inner shadow-black/20">
                <TrendingUp className="w-6 h-6 text-white/30" />
              </div>
              <p className="text-white/50 text-sm mb-4">Your watchlist is empty</p>
              <button
                onClick={() => navigate('/stock-screener')}
                className="px-4 py-2 bg-gradient-to-r from-white/[0.08] to-white/[0.04] border border-white/[0.12] rounded-lg text-white/80 hover:text-white text-sm transition-all duration-200 shadow-inner shadow-black/20"
              >
                Add Stocks
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-3">
              {watchlist.slice(0, 4).map((item) => (
                <div key={item.id} className="p-3 bg-gradient-to-br from-white/[0.03] to-white/[0.01] rounded-lg hover:from-white/[0.05] hover:to-white/[0.02] transition-all duration-200 shadow-inner shadow-black/10 cursor-pointer border border-white/[0.05] hover:border-white/[0.08]">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <div className="font-semibold text-white text-sm">{item.symbol}</div>
                        <div className="text-white/50 text-xs truncate max-w-20">{item.name}</div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="font-medium text-white text-sm">${item.price.toFixed(2)}</div>
                        <div className={`text-xs font-medium ${
                          item.changePercent >= 5 ? 'text-green-300' :
                          item.changePercent >= 0 ? 'text-green-400' :
                          item.changePercent <= -5 ? 'text-red-300' : 'text-red-400'
                        }`}>
                          {item.changePercent >= 0 ? '+' : ''}{item.changePercent.toFixed(1)}%
                        </div>
                      </div>
                    </div>
                    <div className="ml-3">
                      {renderMiniChart(item.changePercent)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );



  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col">
      {/* Clean Headquarters Header */}
      <div className="px-8 py-6 border-b border-white/[0.08] bg-gradient-to-r from-[#0A0A0A] via-[#0A0A0A] to-[#0A0A0A] relative overflow-hidden">
        {/* Subtle green accent line */}
        <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-green-500/30 to-transparent"></div>

        <div className="flex items-center justify-between relative z-10">
          <div>
            <h1 className="text-2xl font-light text-white mb-1">
              Welcome to HQ, <span className="font-medium bg-gradient-to-r from-green-300 to-green-400 bg-clip-text text-transparent">{userName}</span>
            </h1>
            <p className="text-white/70 text-sm">Your trading command center</p>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-sm text-white/60">Active Agents</div>
              <div className="text-lg font-semibold text-green-400">{userAgents.length}</div>
            </div>
            <div className="w-px h-8 bg-white/[0.08]"></div>
            <div className="text-right">
              <div className="text-sm text-white/60">Watchlist</div>
              <div className="text-lg font-semibold text-green-400">{watchlist.length}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8 overflow-hidden">
        <div className="max-w-6xl mx-auto h-full flex flex-col space-y-6">
          {/* Full-Width Horizontal Progress Bar */}
          <div className="w-full">
            {/* Progress Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-xl font-medium text-white mb-1">Get Started Journey</h2>
                <p className="text-white/60 text-sm">Complete these steps to unlock the full power of Osis</p>
              </div>
              <div className="flex items-center gap-4">
                <span className="text-white/70 text-sm font-medium">{completedSteps} of {progressSteps.length} completed</span>
                <div className="text-right">
                  <div className="text-2xl font-bold bg-gradient-to-r from-green-400 to-green-300 bg-clip-text text-transparent">
                    {Math.round((completedSteps / progressSteps.length) * 100)}%
                  </div>
                  <div className="text-white/50 text-xs">Progress</div>
                </div>
              </div>
            </div>

            {/* Full-Width Progress Bar */}
            <div className="w-full h-3 bg-gradient-to-r from-white/[0.05] to-white/[0.08] rounded-full overflow-hidden mb-6 shadow-inner shadow-black/20">
              <div
                className="h-full bg-gradient-to-r from-green-500 via-green-400 to-green-300 transition-all duration-700 ease-out shadow-lg shadow-green-500/20"
                style={{ width: `${(completedSteps / progressSteps.length) * 100}%` }}
              />
            </div>

            {/* Compact Progress Steps */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {progressSteps.map((step, index) => (
                <motion.div
                  key={step.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  whileHover={{ scale: 1.01 }}
                  onClick={step.action}
                  className={`relative p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                    step.completed
                      ? 'bg-gradient-to-br from-green-500/8 to-green-400/4 border-green-500/25 shadow-md'
                      : 'bg-gradient-to-br from-white/[0.02] to-white/[0.01] border-white/[0.06] hover:border-white/[0.12] hover:bg-white/[0.03]'
                  }`}
                >
                  <div className="flex items-center gap-2 mb-1">
                    <div className={`w-5 h-5 rounded-full flex items-center justify-center transition-all duration-200 ${
                      step.completed
                        ? 'bg-gradient-to-r from-green-500 to-green-400'
                        : 'bg-white/[0.06] border border-white/[0.12]'
                    }`}>
                      {step.completed ? (
                        <CheckCircle className="w-3 h-3 text-white" />
                      ) : (
                        <div className="w-1.5 h-1.5 bg-white/40 rounded-full" />
                      )}
                    </div>
                    <h4 className={`font-medium text-xs ${
                      step.completed ? 'text-green-300' : 'text-white'
                    }`}>{step.title}</h4>
                  </div>
                  <p className="text-white/50 text-xs leading-tight pl-7">{step.description}</p>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Compact Bento Grid - New Layout */}
          <div className="flex-1 grid grid-cols-12 gap-4 min-h-0">
            {/* Top Row: Earnings (right) and Watchlist (next to it) */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="col-span-12 md:col-span-6 lg:col-span-4 h-64"
            >
              {renderEarningsCard()}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="col-span-12 md:col-span-6 lg:col-span-4 h-64"
            >
              {renderWatchlistCard()}
            </motion.div>

            {/* Empty space for balance */}
            <div className="hidden lg:block col-span-4"></div>

            {/* Bottom Row: Your Agents - Full Width */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="col-span-12 flex-1"
            >
              {renderAgentsCard()}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
