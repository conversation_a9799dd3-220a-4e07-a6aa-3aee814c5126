import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  CheckCircle,
  Clock,
  TrendingUp,
  Target,
  BarChart3,
  Zap,
  Award,
  ArrowRight,
  Activity,
  Settings,
  DollarSign
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useGamification } from '@/contexts/GamificationContext';

interface UserStats {
  agentsCreated: number;
  scansCompleted: number;
  backtestsCompleted: number;
  portfoliosCreated: number;
  winRate: number;
  totalTrades: number;
  successfulTrades: number;
  timeSavedHours: number;
  lastActivityDate: string | null;
}

const Home: React.FC = () => {
  const navigate = useNavigate();
  const { userProgress, trackAction } = useGamification();

  // State for real user data
  const [userStats, setUserStats] = useState<UserStats>({
    agentsCreated: 0,
    scansCompleted: 0,
    backtestsCompleted: 0,
    portfoliosCreated: 0,
    winRate: 0,
    totalTrades: 0,
    successfulTrades: 0,
    timeSavedHours: 0,
    lastActivityDate: null
  });
  const [isLoading, setIsLoading] = useState(true);

  // Only show get started checklist
  const [availableWidgets, setAvailableWidgets] = useState([
    { id: 'get-started-checklist', name: 'Get Started Checklist', enabled: true, category: 'core', order: 0 },
  ]);

  const [checkedItems, setCheckedItems] = useState<{[key: string]: boolean}>({
    'create-agent': false,
    'first-scan': false,
    'first-backtest': false,
    'portfolio-setup': false,
    'discover-agents': false,
    'make-agent-public': false,
    'setup-marketplace': false
  });

  // Load user data on component mount
  useEffect(() => {
    loadUserData();
  }, []);

  // Reload user data when user progress changes (with stable dependencies)
  const progressDeps = useMemo(() => [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ], [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ]);

  useEffect(() => {
    loadUserData();
  }, progressDeps);

  // Update checked items based on real progress
  useEffect(() => {
    setCheckedItems({
      'create-agent': userStats.agentsCreated > 0,
      'first-scan': userProgress.scansCompleted > 0,
      'first-backtest': userProgress.backtestsCompleted > 0,
      'portfolio-setup': userProgress.portfoliosCreated > 0,
      'discover-agents': userProgress.hasVisitedDiscoverPage,
      'make-agent-public': userProgress.hasCreatedFirstPublicAgent,
      'setup-marketplace': false // This will be updated when user sets up marketplace
    });
  }, [userStats, userProgress]);

  const loadUserData = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Load agent statistics
      const { data: agents } = await supabase
        .from('agents')
        .select('id, created_at')
        .eq('user_id', user.id);

      // Calculate time saved
      const portfolioCount = userProgress.portfoliosCreated;
      const scanTimeMinutes = userProgress.stocksScanned * 1;
      const backtestTimeMinutes = userProgress.backtestsCompleted * 60;
      const portfolioTimeMinutes = portfolioCount * 30;

      const totalTimeMinutes = scanTimeMinutes + backtestTimeMinutes + portfolioTimeMinutes;
      const timeSavedHours = Math.round(totalTimeMinutes / 60 * 10) / 10;

      // Get last activity date from agents
      const lastActivityDate = agents?.length > 0
        ? new Date(Math.max(...agents.map((a: any) => new Date(a.created_at).getTime()))).toISOString()
        : null;

      setUserStats({
        agentsCreated: agents?.length || 0,
        scansCompleted: userProgress.scansCompleted,
        backtestsCompleted: userProgress.backtestsCompleted,
        portfoliosCreated: userProgress.portfoliosCreated,
        winRate: 0,
        totalTrades: userProgress.tradesExecuted,
        successfulTrades: 0,
        timeSavedHours,
        lastActivityDate
      });
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Get started items
  const getStartedItems = [
    {
      id: 'create-agent',
      title: 'Create Your First Agent',
      description: 'Build an AI trading agent to automate your strategy',
      icon: <Zap className="w-5 h-5" />,
      action: () => navigate('/agent-builder'),
      completed: checkedItems['create-agent']
    },
    {
      id: 'first-scan',
      title: 'Run Your First Scan',
      description: 'Discover trading opportunities with stock scanning',
      icon: <Target className="w-5 h-5" />,
      action: () => navigate('/scanner'),
      completed: checkedItems['first-scan']
    },
    {
      id: 'first-backtest',
      title: 'Complete a Backtest',
      description: 'Test your strategy against historical data',
      icon: <BarChart3 className="w-5 h-5" />,
      action: () => navigate('/backtesting'),
      completed: checkedItems['first-backtest']
    },
    {
      id: 'portfolio-setup',
      title: 'Build a Portfolio',
      description: 'Create and optimize your investment portfolio',
      icon: <TrendingUp className="w-5 h-5" />,
      action: () => navigate('/portfolio-manager'),
      completed: checkedItems['portfolio-setup']
    },
    {
      id: 'discover-agents',
      title: 'Explore the Marketplace',
      description: 'Discover and purchase agents created by the community',
      icon: <Activity className="w-5 h-5" />,
      action: () => navigate('/marketplace'),
      completed: checkedItems['discover-agents']
    },
    {
      id: 'make-agent-public',
      title: 'Share Your Agent',
      description: 'Make your agent public for others to use',
      icon: <Award className="w-5 h-5" />,
      action: () => navigate('/agent-builder'),
      completed: checkedItems['make-agent-public']
    },
    {
      id: 'setup-marketplace',
      title: 'Start Selling Agents',
      description: 'Set prices for your agents and start earning money',
      icon: <DollarSign className="w-5 h-5" />,
      action: () => navigate('/settings?tab=marketplace'),
      completed: checkedItems['setup-marketplace']
    }
  ];

  const handleTaskAction = (item: any) => {
    if (item.action) {
      item.action();
    }
  };

  const toggleCheckItem = (id: string) => {
    setCheckedItems(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const completedCount = Object.values(checkedItems).filter(Boolean).length;
  const totalCount = getStartedItems.length;
  const progressPercentage = (completedCount / totalCount) * 100;

  if (isLoading) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
          <p className="text-white/60">Loading your headquarters...</p>
        </div>
      </div>
    );
  }

  // Widget rendering function
  const renderWidget = (widgetId: string) => {
    switch (widgetId) {
      case 'get-started-checklist':
        return (
          <div key={widgetId} className="col-span-full">
            <div className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-medium text-white font-sans">Get Started Checklist</h2>
                <div className="flex items-center gap-2">
                  <span className="text-white/60 text-sm">{completedCount}/{totalCount} completed</span>
                  <div className="w-24 h-2 bg-white/[0.08] rounded-full overflow-hidden">
                    <div
                      className="h-full bg-green-500 transition-all duration-300"
                      style={{ width: `${progressPercentage}%` }}
                    />
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {getStartedItems.map((item) => (
                  <div
                    key={item.id}
                    className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-4 hover:border-white/[0.12] transition-all duration-200 cursor-pointer group"
                    onClick={() => handleTaskAction(item)}
                  >
                    <div className="flex items-center gap-4">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleCheckItem(item.id);
                        }}
                        className="flex-shrink-0"
                      >
                        {item.completed ? (
                          <CheckCircle className="w-6 h-6 text-green-500" />
                        ) : (
                          <div className="w-6 h-6 border-2 border-white/[0.3] rounded-full hover:border-white/[0.5] transition-colors duration-200" />
                        )}
                      </button>
                      <div className="flex items-center gap-3 flex-1">
                        <div className="w-10 h-10 bg-white/[0.05] rounded-lg flex items-center justify-center text-white/70 group-hover:text-white transition-colors duration-200">
                          {item.icon}
                        </div>
                        <div className="flex-1">
                          <h3 className={`font-medium font-sans text-sm mb-1 ${item.completed ? 'text-white/70 line-through' : 'text-white'}`}>
                            {item.title}
                          </h3>
                          <p className="text-white/60 text-xs">{item.description}</p>
                        </div>
                        <ArrowRight className="w-4 h-4 text-white/40 group-hover:text-white/70 transition-colors duration-200" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col">
      {/* Header */}
      <div className="px-8 py-8">
        <h1 className="text-4xl font-normal text-white mb-2 font-sans">
          Welcome to Osis
        </h1>
        {userStats.lastActivityDate && (
          <p className="text-white/60 text-sm font-sans">
            Last active {new Date(userStats.lastActivityDate).toLocaleDateString()}
          </p>
        )}
      </div>

      {/* Main Content */}
      <div className="flex-1 px-8 pb-8">
        <div className="max-w-7xl mx-auto">
          {/* Always show Get Started Checklist */}
          <div className="space-y-8">
            {/* Widget Grid - Only Get Started Checklist */}
            <div className="grid grid-cols-1 gap-6">
              {availableWidgets
                .filter(w => w.enabled)
                .sort((a, b) => (a.order || 0) - (b.order || 0))
                .map(widget =>
                  renderWidget(widget.id)
                )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
