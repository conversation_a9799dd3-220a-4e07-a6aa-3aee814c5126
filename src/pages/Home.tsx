import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CheckCircle,
  TrendingUp,
  Target,
  BarChart3,
  Zap,
  Award,
  ArrowRight,
  Activity,
  DollarSign,
  Plus,
  Eye,
  ShoppingCart,
  Bot,
  LineChart,
  Bookmark,
  Star,
  ArrowUpRight,
  Sparkles,
  Clock,
  Play
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useGamification } from '@/contexts/GamificationContext';
import { useWatchlist } from '@/contexts/WatchlistContext';
import { Progress } from '@/components/ui/progress';
import MiniLine<PERSON><PERSON> from '@/components/MiniLineChart/MiniLineChart';

interface UserStats {
  agentsCreated: number;
  scansCompleted: number;
  backtestsCompleted: number;
  portfoliosCreated: number;
  winRate: number;
  totalTrades: number;
  successfulTrades: number;
  timeSavedHours: number;
  lastActivityDate: string | null;
}

interface WatchlistItem {
  id: string;
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  addedAt: string;
}

interface UserAgent {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  bestBacktest?: {
    totalReturn: number;
    winRate: number;
    totalTrades: number;
    period: string;
  };
  tags: string[];
  isPublic: boolean;
}

interface RecentActivity {
  id: string;
  type: 'purchase' | 'view' | 'backtest' | 'scan' | 'agent_created';
  title: string;
  description: string;
  timestamp: string;
  metadata?: any;
}

interface RecentPurchase {
  id: string;
  agentName: string;
  price: number;
  purchasedAt: string;
  sellerName: string;
}

interface EarningsData {
  totalEarnings: number;
  availableEarnings: number;
  totalSales: number;
  growthPercentage: number;
  recentSales: Array<{
    id: string;
    agentName: string;
    amount: number;
    date: string;
  }>;
}

interface ProgressStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  action: () => void;
}

const Home: React.FC = () => {
  const navigate = useNavigate();
  const { userProgress, trackAction } = useGamification();
  const { watchlist, isLoading: isWatchlistLoading, addToWatchlist, removeFromWatchlist } = useWatchlist();

  // State for real user data
  const [userStats, setUserStats] = useState<UserStats>({
    agentsCreated: 0,
    scansCompleted: 0,
    backtestsCompleted: 0,
    portfoliosCreated: 0,
    winRate: 0,
    totalTrades: 0,
    successfulTrades: 0,
    timeSavedHours: 0,
    lastActivityDate: null
  });
  const [isLoading, setIsLoading] = useState(true);
  const [userName, setUserName] = useState<string>('');

  // New state for enhanced features
  const [userAgents, setUserAgents] = useState<UserAgent[]>([]);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [recentPurchases, setRecentPurchases] = useState<RecentPurchase[]>([]);
  const [earningsData, setEarningsData] = useState<EarningsData>({
    totalEarnings: 0,
    availableEarnings: 0,
    totalSales: 0,
    growthPercentage: 0,
    recentSales: []
  });

  // Enhanced widget system - shows relevant widgets based on user activity
  const [availableWidgets, setAvailableWidgets] = useState([
    { id: 'get-started-checklist', name: 'Get Started Checklist', enabled: true, category: 'core', order: 0 },
    { id: 'watchlist', name: 'Watchlist', enabled: true, category: 'trading', order: 1 },
    { id: 'user-agents', name: 'Your Agents', enabled: true, category: 'agents', order: 2 },
    { id: 'earnings', name: 'Earnings', enabled: true, category: 'marketplace', order: 3 },
    { id: 'recent-activity', name: 'Recent Activity', enabled: true, category: 'activity', order: 4 },
  ]);

  // Progress steps for onboarding
  const progressSteps: ProgressStep[] = [
    {
      id: 'build-agent',
      title: 'Build your first agent',
      description: 'Create a trading strategy',
      completed: userStats.agentsCreated > 0,
      action: () => navigate('/builder')
    },
    {
      id: 'backtest',
      title: 'Backtest a strategy',
      description: 'Test your agent\'s performance',
      completed: userStats.backtestsCompleted > 0,
      action: () => navigate('/backtesting')
    },
    {
      id: 'watchlist',
      title: 'Add to watchlist',
      description: 'Track your favorite stocks',
      completed: watchlist.length > 0,
      action: () => navigate('/stock-search')
    },
    {
      id: 'publish',
      title: 'Publish or sell an agent',
      description: 'Share your strategy with others',
      completed: userAgents.some(agent => agent.isPublic),
      action: () => navigate('/marketplace')
    }
  ];

  const completedSteps = progressSteps.filter(step => step.completed).length;
  const onboardingProgress = (completedSteps / progressSteps.length) * 100;

  // Mock data for development (PROP DATA - not functional)
  const mockWatchlist: WatchlistItem[] = [
    {
      id: '1',
      symbol: 'AAPL',
      name: 'Apple Inc.',
      price: 185.92,
      change: 2.34,
      changePercent: 1.28,
      addedAt: '2024-01-15T10:30:00Z'
    },
    {
      id: '2',
      symbol: 'TSLA',
      name: 'Tesla, Inc.',
      price: 248.50,
      change: -5.20,
      changePercent: -2.05,
      addedAt: '2024-01-14T14:20:00Z'
    },
    {
      id: '3',
      symbol: 'NVDA',
      name: 'NVIDIA Corporation',
      price: 875.30,
      change: 12.45,
      changePercent: 1.44,
      addedAt: '2024-01-13T09:15:00Z'
    }
  ];

  const mockUserAgents: UserAgent[] = [
    {
      id: '1',
      name: 'Momentum Breakout Pro',
      description: 'Advanced momentum trading strategy with breakout detection',
      created_at: '2024-01-10T08:00:00Z',
      bestBacktest: {
        totalReturn: 24.5,
        winRate: 68.2,
        totalTrades: 47,
        period: '3M'
      },
      tags: ['momentum', 'breakout', 'swing'],
      isPublic: true
    },
    {
      id: '2',
      name: 'RSI Divergence Scanner',
      description: 'Identifies RSI divergences for reversal opportunities',
      created_at: '2024-01-08T16:30:00Z',
      bestBacktest: {
        totalReturn: 18.7,
        winRate: 72.1,
        totalTrades: 32,
        period: '2M'
      },
      tags: ['rsi', 'divergence', 'reversal'],
      isPublic: false
    }
  ];

  const mockRecentActivity: RecentActivity[] = [
    {
      id: '1',
      type: 'backtest',
      title: 'Backtest Completed',
      description: 'Momentum Breakout Pro on AAPL - 24.5% return',
      timestamp: '2024-01-15T14:30:00Z'
    },
    {
      id: '2',
      type: 'purchase',
      title: 'Agent Purchased',
      description: 'Bought "Scalping Master v2" from @trader_pro',
      timestamp: '2024-01-15T10:15:00Z'
    },
    {
      id: '3',
      type: 'scan',
      title: 'Stock Scan',
      description: 'Scanned 2,847 stocks for momentum signals',
      timestamp: '2024-01-15T09:45:00Z'
    }
  ];

  const mockRecentPurchases: RecentPurchase[] = [
    {
      id: '1',
      agentName: 'Scalping Master v2',
      price: 29.99,
      purchasedAt: '2024-01-15T10:15:00Z',
      sellerName: 'trader_pro'
    },
    {
      id: '2',
      agentName: 'Options Flow Tracker',
      price: 49.99,
      purchasedAt: '2024-01-12T16:20:00Z',
      sellerName: 'options_guru'
    }
  ];

  const [checkedItems, setCheckedItems] = useState<{[key: string]: boolean}>({
    'create-agent': false,
    'first-scan': false,
    'first-backtest': false,
    'portfolio-setup': false,
    'discover-agents': false,
    'make-agent-public': false,
    'setup-marketplace': false
  });

  // Load user data on component mount
  useEffect(() => {
    loadUserData();
  }, []);

  // Reload user data when user progress changes (with stable dependencies)
  const progressDeps = useMemo(() => [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ], [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ]);

  useEffect(() => {
    loadUserData();
  }, progressDeps);

  // Update checked items based on real progress
  useEffect(() => {
    setCheckedItems({
      'create-agent': userStats.agentsCreated > 0,
      'first-scan': userProgress.scansCompleted > 0,
      'first-backtest': userProgress.backtestsCompleted > 0,
      'portfolio-setup': userProgress.portfoliosCreated > 0,
      'discover-agents': userProgress.hasVisitedDiscoverPage,
      'make-agent-public': userProgress.hasCreatedFirstPublicAgent,
      'setup-marketplace': false // This will be updated when user sets up marketplace
    });
  }, [userStats, userProgress]);

  const loadUserData = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Get user name from metadata or email
      const displayName = user.user_metadata?.full_name ||
                         user.user_metadata?.name ||
                         user.email?.split('@')[0] ||
                         'Trader';
      setUserName(displayName);

      // Load agent statistics with full details for enhanced features
      const { data: agents } = await supabase
        .from('agents')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      // Fetch recent purchases (FUNCTIONAL)
      const { data: purchases } = await supabase
        .from('purchased_agents')
        .select(`
          *,
          agents!original_agent_id(name),
          profiles!seller_id(full_name)
        `)
        .eq('buyer_id', user.id)
        .order('created_at', { ascending: false })
        .limit(5);

      // Fetch recent activity (FUNCTIONAL)
      const { data: activities } = await supabase
        .from('user_activity_log')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10);

      // Calculate time saved
      const portfolioCount = userProgress.portfoliosCreated;
      const scanTimeMinutes = userProgress.stocksScanned * 1;
      const backtestTimeMinutes = userProgress.backtestsCompleted * 60;
      const portfolioTimeMinutes = portfolioCount * 30;

      const totalTimeMinutes = scanTimeMinutes + backtestTimeMinutes + portfolioTimeMinutes;
      const timeSavedHours = Math.round(totalTimeMinutes / 60 * 10) / 10;

      // Get last activity date from agents
      const lastActivityDate = agents?.length > 0
        ? new Date(Math.max(...agents.map((a: any) => new Date(a.created_at).getTime()))).toISOString()
        : null;

      setUserStats({
        agentsCreated: agents?.length || 0,
        scansCompleted: userProgress.scansCompleted,
        backtestsCompleted: userProgress.backtestsCompleted,
        portfoliosCreated: userProgress.portfoliosCreated,
        winRate: 0,
        totalTrades: userProgress.tradesExecuted,
        successfulTrades: 0,
        timeSavedHours,
        lastActivityDate
      });

      // Set mock data for development (PROP DATA)
      setUserAgents(mockUserAgents);
      setRecentActivity(mockRecentActivity);
      setRecentPurchases(mockRecentPurchases);

      // Determine which widgets to show based on user activity
      const hasAgents = (agents?.length || 0) > 0;
      const hasPurchases = (purchases?.length || 0) > 0;
      const hasActivity = (activities?.length || 0) > 0;
      const hasAnyActivity = hasAgents || hasPurchases || hasActivity;

      setAvailableWidgets(prev => prev.map(widget => ({
        ...widget,
        enabled: widget.id === 'get-started-checklist' ? !hasAnyActivity :
                 widget.id === 'watchlist' ? true : // Always show watchlist
                 widget.id === 'user-agents' ? hasAgents :
                 widget.id === 'recent-activity' ? hasActivity :
                 widget.id === 'recent-purchases' ? hasPurchases :
                 widget.enabled
      })));

    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Watchlist functions now handled by useWatchlist context

  // Get started items
  const getStartedItems = [
    {
      id: 'create-agent',
      title: 'Create Your First Agent',
      description: 'Build an AI trading agent to automate your strategy',
      icon: <Zap className="w-5 h-5" />,
      action: () => navigate('/agent-builder'),
      completed: checkedItems['create-agent']
    },
    {
      id: 'first-scan',
      title: 'Run Your First Scan',
      description: 'Discover trading opportunities with stock scanning',
      icon: <Target className="w-5 h-5" />,
      action: () => navigate('/scanner'),
      completed: checkedItems['first-scan']
    },
    {
      id: 'first-backtest',
      title: 'Complete a Backtest',
      description: 'Test your strategy against historical data',
      icon: <BarChart3 className="w-5 h-5" />,
      action: () => navigate('/backtesting'),
      completed: checkedItems['first-backtest']
    },
    {
      id: 'portfolio-setup',
      title: 'Build a Portfolio',
      description: 'Create and optimize your investment portfolio',
      icon: <TrendingUp className="w-5 h-5" />,
      action: () => navigate('/portfolio-manager'),
      completed: checkedItems['portfolio-setup']
    },
    {
      id: 'discover-agents',
      title: 'Explore the Marketplace',
      description: 'Discover and purchase agents created by the community',
      icon: <Activity className="w-5 h-5" />,
      action: () => navigate('/marketplace'),
      completed: checkedItems['discover-agents']
    },
    {
      id: 'make-agent-public',
      title: 'Share Your Agent',
      description: 'Make your agent public for others to use',
      icon: <Award className="w-5 h-5" />,
      action: () => navigate('/agent-builder'),
      completed: checkedItems['make-agent-public']
    },
    {
      id: 'setup-marketplace',
      title: 'Start Selling Agents',
      description: 'Set prices for your agents and start earning money',
      icon: <DollarSign className="w-5 h-5" />,
      action: () => navigate('/settings?tab=marketplace'),
      completed: checkedItems['setup-marketplace']
    }
  ];

  const handleTaskAction = (item: any) => {
    if (item.action) {
      item.action();
    }
  };

  const toggleCheckItem = (id: string) => {
    setCheckedItems(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const completedCount = Object.values(checkedItems).filter(Boolean).length;
  const totalCount = getStartedItems.length;
  const progressPercentage = (completedCount / totalCount) * 100;

  if (isLoading) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
          <p className="text-white/60">Loading your headquarters...</p>
        </div>
      </div>
    );
  }

  // Bento Card Render Functions
  const renderAgentsCard = () => (
    <div className="h-full bg-gradient-to-br from-[#1A1A1A] to-[#0F0F0F] border border-white/[0.08] rounded-2xl p-6 shadow-2xl shadow-black/40 hover:border-white/[0.12] transition-all duration-300 group">
      <div className="absolute inset-0 rounded-2xl shadow-inner shadow-black/30 pointer-events-none"></div>
      <div className="relative z-10 h-full flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <motion.div
              whileHover={{ scale: 1.1, rotate: 5 }}
              className="w-10 h-10 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-xl flex items-center justify-center shadow-lg"
            >
              <Bot className="w-5 h-5 text-blue-400" />
            </motion.div>
            <div>
              <h3 className="text-lg font-medium text-white">Your Agents</h3>
              <p className="text-white/60 text-sm">Trading strategies you've built</p>
            </div>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => navigate('/builder')}
            className="px-4 py-2 bg-gradient-to-r from-green-500/20 to-green-400/20 border border-green-500/30 rounded-lg text-green-400 text-sm font-medium hover:from-green-500/30 hover:to-green-400/30 transition-all duration-300 shadow-lg"
          >
            <Plus className="w-4 h-4 inline mr-2" />
            New Agent
          </motion.button>
        </div>

        <div className="flex-1">
          {userAgents.length === 0 ? (
            <div className="h-full flex flex-col items-center justify-center text-center py-8">
              <motion.div
                animate={{ y: [0, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="w-16 h-16 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-2xl flex items-center justify-center mb-4 shadow-lg"
              >
                <Bot className="w-8 h-8 text-blue-400/60" />
              </motion.div>
              <h4 className="text-white/80 font-medium mb-2">No agents yet</h4>
              <p className="text-white/50 text-sm mb-4 max-w-xs">Create your first trading agent to start building automated strategies</p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/builder')}
                className="px-6 py-3 bg-gradient-to-r from-green-500 to-green-400 text-white rounded-xl font-medium shadow-lg hover:shadow-green-500/25 transition-all duration-300"
              >
                Build First Agent
              </motion.button>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 h-full">
              {userAgents.slice(0, 4).map((agent, index) => (
                <motion.div
                  key={agent.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.02, y: -2 }}
                  className="bg-white/[0.03] border border-white/[0.08] rounded-xl p-4 hover:border-white/[0.15] transition-all duration-300 cursor-pointer shadow-lg"
                  onClick={() => navigate(`/agent/${agent.id}`)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <h4 className="text-white font-medium text-sm truncate">{agent.name}</h4>
                      <p className="text-white/60 text-xs mt-1 line-clamp-2">{agent.description || 'No description'}</p>
                    </div>
                    {agent.isPublic && (
                      <div className="w-2 h-2 bg-green-400 rounded-full ml-2 mt-1 shadow-lg shadow-green-400/50"></div>
                    )}
                  </div>

                  {agent.bestBacktest && (
                    <div className="flex items-center justify-between text-xs">
                      <div className="flex items-center gap-2">
                        <TrendingUp className="w-3 h-3 text-green-400" />
                        <span className="text-green-400 font-medium">
                          +{agent.bestBacktest.totalReturn.toFixed(1)}%
                        </span>
                      </div>
                      <div className="text-white/50">
                        {agent.bestBacktest.winRate.toFixed(0)}% win rate
                      </div>
                    </div>
                  )}
                </motion.div>
              ))}

              {userAgents.length > 4 && (
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-4 flex items-center justify-center cursor-pointer hover:border-white/[0.15] transition-all duration-300"
                  onClick={() => navigate('/agents')}
                >
                  <div className="text-center">
                    <div className="text-white/60 text-sm font-medium">+{userAgents.length - 4} more</div>
                    <div className="text-white/40 text-xs">View all agents</div>
                  </div>
                </motion.div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderEarningsCard = () => {
    // Mock earnings data for now - will be replaced with real data from marketplace
    const mockEarnings = {
      totalEarnings: 1247.50,
      availableEarnings: 892.30,
      totalSales: 23,
      growthPercentage: 15.7
    };

    return (
      <div className="h-full bg-gradient-to-br from-[#1A1A1A] to-[#0F0F0F] border border-white/[0.08] rounded-2xl p-6 shadow-2xl shadow-black/40 hover:border-white/[0.12] transition-all duration-300 group relative overflow-hidden">
        <div className="absolute inset-0 rounded-2xl shadow-inner shadow-black/30 pointer-events-none"></div>

        {/* Subtle animated background */}
        <motion.div
          animate={{
            background: [
              'radial-gradient(circle at 20% 80%, rgba(34, 197, 94, 0.03) 0%, transparent 50%)',
              'radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.05) 0%, transparent 50%)',
              'radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.03) 0%, transparent 50%)'
            ]
          }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
          className="absolute inset-0 rounded-2xl"
        />

        <div className="relative z-10 h-full flex flex-col">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                animate={{ scale: [1, 1.05, 1] }}
                transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                className="w-10 h-10 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-xl flex items-center justify-center shadow-lg"
              >
                <DollarSign className="w-5 h-5 text-green-400" />
              </motion.div>
              <div>
                <h3 className="text-lg font-medium text-white">Earnings</h3>
                <p className="text-white/60 text-sm">Revenue from agent sales</p>
              </div>
            </div>

            {mockEarnings.growthPercentage > 0 && (
              <motion.div
                animate={{ y: [0, -2, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="flex items-center gap-1 px-2 py-1 bg-green-500/20 border border-green-500/30 rounded-lg"
              >
                <ArrowUpRight className="w-3 h-3 text-green-400" />
                <span className="text-green-400 text-xs font-medium">+{mockEarnings.growthPercentage}%</span>
              </motion.div>
            )}
          </div>

          <div className="flex-1 flex flex-col justify-center">
            <div className="text-center mb-6">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="text-4xl font-bold text-white mb-2"
              >
                ${mockEarnings.totalEarnings.toLocaleString()}
              </motion.div>
              <p className="text-white/60 text-sm">Total earned</p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-white/[0.03] rounded-xl border border-white/[0.05]">
                <span className="text-white/70 text-sm">Available</span>
                <span className="text-green-400 font-medium">${mockEarnings.availableEarnings.toLocaleString()}</span>
              </div>

              <div className="flex items-center justify-between p-3 bg-white/[0.03] rounded-xl border border-white/[0.05]">
                <span className="text-white/70 text-sm">Total Sales</span>
                <span className="text-white font-medium">{mockEarnings.totalSales}</span>
              </div>
            </div>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => navigate('/marketplace?tab=earnings')}
              className="mt-4 w-full py-3 bg-gradient-to-r from-green-500/20 to-green-400/20 border border-green-500/30 rounded-xl text-green-400 font-medium hover:from-green-500/30 hover:to-green-400/30 transition-all duration-300 shadow-lg"
            >
              View Details
            </motion.button>
          </div>
        </div>
      </div>
    );
  };

  const renderWatchlistCard = () => (
    <div className="h-full bg-gradient-to-br from-[#1A1A1A] to-[#0F0F0F] border border-white/[0.08] rounded-2xl p-6 shadow-2xl shadow-black/40 hover:border-white/[0.12] transition-all duration-300 group">
      <div className="absolute inset-0 rounded-2xl shadow-inner shadow-black/30 pointer-events-none"></div>
      <div className="relative z-10 h-full flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <motion.div
              whileHover={{ scale: 1.1, rotate: 5 }}
              className="w-10 h-10 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-xl flex items-center justify-center shadow-lg"
            >
              <Star className="w-5 h-5 text-blue-400" />
            </motion.div>
            <div>
              <h3 className="text-lg font-medium text-white">Watchlist</h3>
              <p className="text-white/60 text-sm">Your tracked stocks</p>
            </div>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => navigate('/stock-screener')}
            className="px-4 py-2 bg-gradient-to-r from-blue-500/20 to-cyan-400/20 border border-blue-500/30 rounded-lg text-blue-400 text-sm font-medium hover:from-blue-500/30 hover:to-cyan-400/30 transition-all duration-300 shadow-lg"
          >
            <Plus className="w-4 h-4 inline mr-2" />
            Add Stock
          </motion.button>
        </div>

        <div className="flex-1">
          {watchlist.length === 0 ? (
            <div className="h-full flex flex-col items-center justify-center text-center py-8">
              <motion.div
                animate={{ y: [0, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="w-16 h-16 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-2xl flex items-center justify-center mb-4 shadow-lg"
              >
                <Star className="w-8 h-8 text-blue-400/60" />
              </motion.div>
              <h4 className="text-white/80 font-medium mb-2">No stocks tracked</h4>
              <p className="text-white/50 text-sm mb-4 max-w-xs">Add stocks to your watchlist to track their performance</p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/stock-screener')}
                className="px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-400 text-white rounded-xl font-medium shadow-lg hover:shadow-blue-500/25 transition-all duration-300"
              >
                Browse Stocks
              </motion.button>
            </div>
          ) : (
            <div className="space-y-3 h-full overflow-y-auto">
              {watchlist.slice(0, 6).map((stock, index) => (
                <motion.div
                  key={stock.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.02, x: 4 }}
                  className="bg-white/[0.03] border border-white/[0.08] rounded-xl p-4 hover:border-white/[0.15] transition-all duration-300 cursor-pointer shadow-lg"
                  onClick={() => navigate(`/stock-screener/${stock.symbol}`)}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <h4 className="text-white font-medium text-sm">{stock.symbol}</h4>
                      <p className="text-white/60 text-xs truncate">{stock.name}</p>
                    </div>
                    <div className="text-right ml-3">
                      <div className="text-white font-medium text-sm">${stock.price.toFixed(2)}</div>
                      <div className={`text-xs font-medium ${stock.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {stock.change >= 0 ? '+' : ''}{stock.changePercent.toFixed(2)}%
                      </div>
                    </div>
                  </div>

                  {/* Mini line chart */}
                  <div className="h-8">
                    <MiniLineChart
                      data={stock.priceHistory || []}
                      color={stock.change >= 0 ? '#22c55e' : '#ef4444'}
                      height={32}
                    />
                  </div>
                </motion.div>
              ))}

              {watchlist.length > 6 && (
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-4 flex items-center justify-center cursor-pointer hover:border-white/[0.15] transition-all duration-300"
                  onClick={() => navigate('/stock-screener')}
                >
                  <div className="text-center">
                    <div className="text-white/60 text-sm font-medium">+{watchlist.length - 6} more</div>
                    <div className="text-white/40 text-xs">View all stocks</div>
                  </div>
                </motion.div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderRecentActivityCard = () => (
    <div className="h-full bg-gradient-to-br from-[#1A1A1A] to-[#0F0F0F] border border-white/[0.08] rounded-2xl p-6 shadow-2xl shadow-black/40 hover:border-white/[0.12] transition-all duration-300 group">
      <div className="absolute inset-0 rounded-2xl shadow-inner shadow-black/30 pointer-events-none"></div>
      <div className="relative z-10 h-full flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <motion.div
              whileHover={{ scale: 1.1, rotate: 5 }}
              className="w-10 h-10 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-xl flex items-center justify-center shadow-lg"
            >
              <Clock className="w-5 h-5 text-purple-400" />
            </motion.div>
            <div>
              <h3 className="text-lg font-medium text-white">Recent Activity</h3>
              <p className="text-white/60 text-sm">Your latest actions</p>
            </div>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => navigate('/activity')}
            className="px-4 py-2 bg-gradient-to-r from-purple-500/20 to-pink-400/20 border border-purple-500/30 rounded-lg text-purple-400 text-sm font-medium hover:from-purple-500/30 hover:to-pink-400/30 transition-all duration-300 shadow-lg"
          >
            View All
          </motion.button>
        </div>

        <div className="flex-1">
          {recentActivity.length === 0 ? (
            <div className="h-full flex flex-col items-center justify-center text-center py-8">
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                className="w-16 h-16 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-2xl flex items-center justify-center mb-4 shadow-lg"
              >
                <Clock className="w-8 h-8 text-purple-400/60" />
              </motion.div>
              <h4 className="text-white/80 font-medium mb-2">No recent activity</h4>
              <p className="text-white/50 text-sm max-w-xs">Start trading to see your activity here</p>
            </div>
          ) : (
            <div className="space-y-3 h-full overflow-y-auto">
              {recentActivity.slice(0, 5).map((activity, index) => (
                <motion.div
                  key={activity.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.02, y: -2 }}
                  className="bg-white/[0.03] border border-white/[0.08] rounded-xl p-4 hover:border-white/[0.15] transition-all duration-300 shadow-lg"
                >
                  <div className="flex items-start gap-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      activity.type === 'agent_created' ? 'bg-blue-500/20 text-blue-400' :
                      activity.type === 'backtest_completed' ? 'bg-green-500/20 text-green-400' :
                      activity.type === 'agent_published' ? 'bg-purple-500/20 text-purple-400' :
                      'bg-white/[0.1] text-white/60'
                    }`}>
                      {activity.type === 'agent_created' && <Bot className="w-4 h-4" />}
                      {activity.type === 'backtest_completed' && <TrendingUp className="w-4 h-4" />}
                      {activity.type === 'agent_published' && <Sparkles className="w-4 h-4" />}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-white font-medium text-sm mb-1">{activity.title}</h4>
                      <p className="text-white/60 text-xs line-clamp-2">{activity.description}</p>
                      <div className="text-white/40 text-xs mt-2">
                        {new Date(activity.timestamp).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderWidget = (widgetId: string) => {
    switch (widgetId) {
      case 'agents':
        return renderAgentsCard();
      case 'earnings':
        return renderEarningsCard();
      case 'watchlist':
        return renderWatchlistCard();
      case 'recent-activity':
        return renderRecentActivityCard();
      case 'get-started-checklist':
        return (
          <div key={widgetId} className="col-span-full">
            <div className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-medium text-white font-sans">Get Started Checklist</h2>
                <div className="flex items-center gap-2">
                  <span className="text-white/60 text-sm">{completedCount}/{totalCount} completed</span>
                  <div className="w-24 h-2 bg-white/[0.08] rounded-full overflow-hidden">
                    <div
                      className="h-full bg-green-500 transition-all duration-300"
                      style={{ width: `${progressPercentage}%` }}
                    />
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {getStartedItems.map((item) => (
                  <div
                    key={item.id}
                    className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-4 hover:border-white/[0.12] transition-all duration-200 cursor-pointer group"
                    onClick={() => handleTaskAction(item)}
                  >
                    <div className="flex items-center gap-4">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleCheckItem(item.id);
                        }}
                        className="flex-shrink-0"
                      >
                        {item.completed ? (
                          <CheckCircle className="w-6 h-6 text-green-500" />
                        ) : (
                          <div className="w-6 h-6 border-2 border-white/[0.3] rounded-full hover:border-white/[0.5] transition-colors duration-200" />
                        )}
                      </button>
                      <div className="flex items-center gap-3 flex-1">
                        <div className="w-10 h-10 bg-white/[0.05] rounded-lg flex items-center justify-center text-white/70 group-hover:text-white transition-colors duration-200">
                          {item.icon}
                        </div>
                        <div className="flex-1">
                          <h3 className={`font-medium font-sans text-sm mb-1 ${item.completed ? 'text-white/70 line-through' : 'text-white'}`}>
                            {item.title}
                          </h3>
                          <p className="text-white/60 text-xs">{item.description}</p>
                        </div>
                        <ArrowRight className="w-4 h-4 text-white/40 group-hover:text-white/70 transition-colors duration-200" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'watchlist':
        return (
          <div key={widgetId} className="col-span-1">
            <div className="h-full bg-[#1A1A1A] border border-white/[0.08] rounded-lg p-5 shadow-xl shadow-black/40 relative overflow-hidden hover:border-white/[0.12] transition-all duration-300">
              {/* Inner shadow overlay */}
              <div className="absolute inset-0 rounded-lg shadow-inner shadow-black/30 pointer-events-none"></div>

              <div className="relative z-10 h-full flex flex-col">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <div className="w-5 h-5 bg-white/[0.05] rounded flex items-center justify-center shadow-inner shadow-black/20">
                      <Bookmark className="w-3 h-3 text-white/60" />
                    </div>
                    <h3 className="text-sm font-medium text-white">Watchlist</h3>
                  </div>
                  <button
                    onClick={() => addToWatchlist({ symbol: 'MSFT', name: 'Microsoft Corporation' })}
                    disabled={isWatchlistLoading}
                    className="w-5 h-5 bg-white/[0.03] hover:bg-white/[0.08] border border-white/[0.08] rounded flex items-center justify-center transition-all duration-200 shadow-inner shadow-black/20"
                  >
                    <Plus className="w-2.5 h-2.5 text-white/60" />
                  </button>
                </div>

                <div className="flex-1">
                  {watchlist.length === 0 ? (
                    <div className="text-center py-6">
                      <div className="w-8 h-8 bg-white/[0.02] rounded flex items-center justify-center mx-auto mb-2 shadow-inner shadow-black/20">
                        <TrendingUp className="w-4 h-4 text-white/30" />
                      </div>
                      <p className="text-white/50 text-xs mb-3">Empty watchlist</p>
                      <button
                        onClick={() => addToWatchlist({ symbol: 'AAPL', name: 'Apple Inc.' })}
                        className="px-3 py-1.5 bg-white/[0.05] hover:bg-white/[0.08] border border-white/[0.08] rounded text-white/60 hover:text-white text-xs transition-all duration-200 shadow-inner shadow-black/20"
                      >
                        Add Stock
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {watchlist.slice(0, 3).map((item) => (
                        <div key={item.id} className="flex items-center justify-between p-2.5 bg-white/[0.02] rounded hover:bg-white/[0.04] transition-all duration-200 shadow-inner shadow-black/10">
                          <div className="flex items-center gap-2 flex-1 min-w-0">
                            <div className="text-left">
                              <div className="font-medium text-white text-xs">{item.symbol}</div>
                              <div className="text-white/50 text-[10px] truncate max-w-[60px]">{item.name}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium text-white text-xs">${item.price.toFixed(0)}</div>
                            <div className={`text-[10px] ${item.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                              {item.changePercent >= 0 ? '+' : ''}{item.changePercent.toFixed(1)}%
                            </div>
                          </div>
                        </div>
                      ))}
                      {watchlist.length > 3 && (
                        <button
                          onClick={() => navigate('/stock-screener')}
                          className="w-full p-2 text-white/50 hover:text-white/70 text-xs border border-white/[0.05] rounded hover:border-white/[0.1] transition-all duration-200 shadow-inner shadow-black/10"
                        >
                          +{watchlist.length - 3} more
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );

      case 'user-agents':
        return (
          <div key={widgetId} className="col-span-2">
            <div className="h-full bg-[#1A1A1A] border border-white/[0.08] rounded-lg p-5 shadow-xl shadow-black/40 relative overflow-hidden hover:border-white/[0.12] transition-all duration-300">
              {/* Inner shadow overlay */}
              <div className="absolute inset-0 rounded-lg shadow-inner shadow-black/30 pointer-events-none"></div>

              <div className="relative z-10 h-full flex flex-col">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <div className="w-5 h-5 bg-white/[0.05] rounded flex items-center justify-center shadow-inner shadow-black/20">
                      <Bot className="w-3 h-3 text-white/60" />
                    </div>
                    <h3 className="text-sm font-medium text-white">Your Agents</h3>
                  </div>
                  <button
                    onClick={() => navigate('/agent-builder')}
                    className="w-5 h-5 bg-white/[0.03] hover:bg-white/[0.08] border border-white/[0.08] rounded flex items-center justify-center transition-all duration-200 shadow-inner shadow-black/20"
                  >
                    <Plus className="w-2.5 h-2.5 text-white/60" />
                  </button>
                </div>

                <div className="flex-1 grid grid-cols-2 gap-3">
                  {userAgents.slice(0, 2).map((agent) => (
                    <div key={agent.id} className="p-3 bg-white/[0.02] rounded hover:bg-white/[0.04] transition-all duration-200 cursor-pointer shadow-inner shadow-black/10">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-1 mb-1">
                            <h4 className="font-medium text-white text-xs truncate">{agent.name}</h4>
                            {agent.isPublic && (
                              <div className="w-1.5 h-1.5 bg-white/40 rounded-full flex-shrink-0"></div>
                            )}
                          </div>
                          <p className="text-white/50 text-[10px] line-clamp-2">{agent.description}</p>
                        </div>
                      </div>

                      {agent.bestBacktest && (
                        <div className="grid grid-cols-3 gap-2 pt-2 border-t border-white/[0.05]">
                          <div className="text-center">
                            <div className={`font-medium text-xs ${agent.bestBacktest.totalReturn >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                              {agent.bestBacktest.totalReturn >= 0 ? '+' : ''}{agent.bestBacktest.totalReturn}%
                            </div>
                            <div className="text-white/40 text-[9px]">Return</div>
                          </div>
                          <div className="text-center">
                            <div className="text-white font-medium text-xs">{agent.bestBacktest.winRate}%</div>
                            <div className="text-white/40 text-[9px]">Win</div>
                          </div>
                          <div className="text-center">
                            <div className="text-white font-medium text-xs">{agent.bestBacktest.totalTrades}</div>
                            <div className="text-white/40 text-[9px]">Trades</div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {userAgents.length === 0 && (
                  <div className="text-center py-6">
                    <div className="w-8 h-8 bg-white/[0.02] rounded-full flex items-center justify-center mx-auto mb-2 shadow-inner shadow-black/20">
                      <Bot className="w-4 h-4 text-white/30" />
                    </div>
                    <p className="text-white/50 text-xs mb-3">No agents created</p>
                    <button
                      onClick={() => navigate('/agent-builder')}
                      className="px-3 py-1.5 bg-blue-500/10 hover:bg-blue-500/20 border border-blue-500/20 rounded-md text-blue-400 text-xs transition-all duration-200 shadow-inner shadow-blue-500/5"
                    >
                      Create Agent
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 'performance-chart':
        return (
          <div key={widgetId} className="col-span-2">
            <div className="h-full bg-[#1A1A1A] border border-white/[0.08] rounded-lg p-5 shadow-xl shadow-black/40 relative overflow-hidden hover:border-white/[0.12] transition-all duration-300">
              {/* Inner shadow overlay */}
              <div className="absolute inset-0 rounded-lg shadow-inner shadow-black/30 pointer-events-none"></div>

              <div className="relative z-10 h-full flex flex-col">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <div className="w-5 h-5 bg-white/[0.05] rounded flex items-center justify-center shadow-inner shadow-black/20">
                      <TrendingUp className="w-3 h-3 text-white/60" />
                    </div>
                    <h3 className="text-sm font-medium text-white">Portfolio Performance</h3>
                  </div>
                  <div className="text-right">
                    <div className="text-green-400 text-sm font-medium">+24.5%</div>
                    <div className="text-white/50 text-xs">This month</div>
                  </div>
                </div>

                {/* Mini Chart */}
                <div className="flex-1 h-20 relative">
                  <svg className="w-full h-full" viewBox="0 0 200 60" preserveAspectRatio="none">
                    <defs>
                      <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" stopColor="rgb(34, 197, 94)" stopOpacity="0.3"/>
                        <stop offset="100%" stopColor="rgb(34, 197, 94)" stopOpacity="0.05"/>
                      </linearGradient>
                    </defs>
                    <path
                      d="M0,45 Q25,35 50,30 T100,25 Q125,20 150,15 T200,10"
                      fill="none"
                      stroke="rgb(34, 197, 94)"
                      strokeWidth="1.5"
                      className="drop-shadow-sm"
                    />
                    <path
                      d="M0,45 Q25,35 50,30 T100,25 Q125,20 150,15 T200,10 L200,60 L0,60 Z"
                      fill="url(#chartGradient)"
                    />
                  </svg>
                </div>

                {/* Stats Row */}
                <div className="grid grid-cols-3 gap-3 mt-3 pt-3 border-t border-white/[0.05]">
                  <div className="text-center">
                    <div className="text-white font-medium text-xs">{userStats.totalTrades}</div>
                    <div className="text-white/40 text-[9px]">Trades</div>
                  </div>
                  <div className="text-center">
                    <div className="text-white font-medium text-xs">68%</div>
                    <div className="text-white/40 text-[9px]">Win Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-white font-medium text-xs">${(userStats.totalTrades * 1247).toLocaleString()}</div>
                    <div className="text-white/40 text-[9px]">Volume</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'recent-activity':
        return (
          <div key={widgetId} className="col-span-1">
            <div className="h-full bg-[#1A1A1A] border border-white/[0.08] rounded-lg p-5 shadow-xl shadow-black/40 relative overflow-hidden hover:border-white/[0.12] transition-all duration-300">
              {/* Inner shadow overlay */}
              <div className="absolute inset-0 rounded-lg shadow-inner shadow-black/30 pointer-events-none"></div>

              <div className="relative z-10 h-full flex flex-col">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-5 h-5 bg-white/[0.05] rounded flex items-center justify-center shadow-inner shadow-black/20">
                    <Activity className="w-3 h-3 text-white/60" />
                  </div>
                  <h3 className="text-sm font-medium text-white">Activity</h3>
                </div>

                <div className="flex-1 space-y-2">
                  {recentActivity.slice(0, 4).map((activity) => (
                    <div key={activity.id} className="flex items-start gap-2 p-2.5 bg-white/[0.02] rounded hover:bg-white/[0.04] transition-all duration-200 shadow-inner shadow-black/10">
                      <div className="w-4 h-4 bg-white/[0.03] rounded flex items-center justify-center flex-shrink-0 mt-0.5 shadow-inner shadow-black/20">
                        {activity.type === 'backtest' && <LineChart className="w-2 h-2 text-white/60" />}
                        {activity.type === 'purchase' && <ShoppingCart className="w-2 h-2 text-white/60" />}
                        {activity.type === 'scan' && <Target className="w-2 h-2 text-white/60" />}
                        {activity.type === 'agent_created' && <Bot className="w-2 h-2 text-white/60" />}
                        {activity.type === 'view' && <Eye className="w-2 h-2 text-white/60" />}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-white text-xs mb-0.5 truncate">{activity.title}</div>
                        <div className="text-white/50 text-[10px] line-clamp-1">{activity.description}</div>
                        <div className="text-white/30 text-[9px] mt-0.5">
                          {new Date(activity.timestamp).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      case 'recent-purchases':
        return (
          <div key={widgetId} className="col-span-full lg:col-span-1">
            <div className="bg-gradient-to-br from-white/[0.03] to-white/[0.01] border border-white/[0.08] rounded-xl p-6 backdrop-blur-sm">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-green-500/20 to-green-600/10 rounded-lg flex items-center justify-center">
                  <ShoppingCart className="w-5 h-5 text-green-400" />
                </div>
                <h2 className="text-xl font-medium text-white">Recent Purchases</h2>
              </div>

              <div className="space-y-3">
                {recentPurchases.slice(0, 4).map((purchase) => (
                  <div key={purchase.id} className="flex items-center justify-between p-3 bg-white/[0.02] rounded-lg hover:bg-white/[0.04] transition-all duration-200 group">
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-white text-sm mb-1 truncate">{purchase.agentName}</div>
                      <div className="text-white/60 text-xs">by @{purchase.sellerName}</div>
                    </div>
                    <div className="text-right ml-3">
                      <div className="font-medium text-green-400 text-sm">${purchase.price}</div>
                      <div className="text-white/40 text-xs">
                        {new Date(purchase.purchasedAt).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                ))}

                <button
                  onClick={() => navigate('/marketplace?tab=purchased')}
                  className="w-full p-3 text-white/60 hover:text-white text-sm border border-white/[0.08] rounded-lg hover:border-white/[0.15] transition-all duration-200"
                >
                  View All Purchases
                </button>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col">
      {/* Clean Headquarters Header */}
      <div className="px-8 py-6 border-b border-white/[0.08]">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-medium text-white mb-1">
              Welcome to HQ, <span className="bg-gradient-to-r from-green-400 to-green-300 bg-clip-text text-transparent">{userName}</span>
            </h1>
            <p className="text-white/60 text-sm">Your trading command center</p>
            {userStats.lastActivityDate && (
              <div className="flex items-center gap-2 mt-2">
                <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
                <p className="text-white/50 text-xs">
                  Last active {new Date(userStats.lastActivityDate).toLocaleDateString()}
                </p>
              </div>
            )}
          </div>

          {/* Mini Stats Row */}
          <div className="flex items-center gap-6">
            <div className="text-center">
              <div className="text-white text-lg font-medium">{userStats.timeSavedHours}h</div>
              <div className="text-white/50 text-xs">Saved</div>
            </div>
            <div className="w-px h-8 bg-white/[0.1]"></div>
            <div className="text-center">
              <div className="text-white text-lg font-medium">{userStats.agentsCreated}</div>
              <div className="text-white/50 text-xs">Agents</div>
            </div>
            <div className="w-px h-8 bg-white/[0.1]"></div>
            <div className="text-center">
              <div className="text-blue-400 text-lg font-medium">{userStats.scansCompleted}</div>
              <div className="text-white/50 text-xs">Scans</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Bento Grid Content */}
      <div className="flex-1 p-8 overflow-y-auto">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Horizontal Progress Bar */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-br from-[#1A1A1A] to-[#0F0F0F] border border-white/[0.08] rounded-2xl p-6 shadow-2xl shadow-black/40"
          >
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-medium text-white">Get Started Journey</h3>
                <p className="text-white/60 text-sm">Complete these steps to unlock Osis's full potential</p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-white">{completedSteps}/{progressSteps.length}</div>
                <div className="text-white/60 text-sm">completed</div>
              </div>
            </div>

            <div className="mb-4">
              <Progress
                value={onboardingProgress}
                className="h-3 bg-white/[0.05]"
                indicatorClassName="bg-gradient-to-r from-green-400 to-green-300"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {progressSteps.map((step, index) => (
                <motion.div
                  key={step.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  onClick={step.action}
                  className={`p-4 rounded-xl border cursor-pointer transition-all duration-300 ${
                    step.completed
                      ? 'bg-green-500/10 border-green-500/30 shadow-lg shadow-green-500/10'
                      : 'bg-white/[0.02] border-white/[0.08] hover:border-white/[0.15]'
                  }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                      step.completed ? 'bg-green-500' : 'bg-white/[0.1]'
                    }`}>
                      {step.completed ? (
                        <CheckCircle className="w-4 h-4 text-white" />
                      ) : (
                        <div className="w-2 h-2 bg-white/40 rounded-full" />
                      )}
                    </div>
                    <h4 className="text-white font-medium text-sm">{step.title}</h4>
                  </div>
                  <p className="text-white/60 text-xs">{step.description}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Asymmetric Bento Grid */}
          <div className="grid grid-cols-12 gap-6 auto-rows-fr">
            {/* Your Agents - Wide Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="col-span-12 lg:col-span-8 row-span-1"
            >
              {renderAgentsCard()}
            </motion.div>

            {/* Earnings - Prominent Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="col-span-12 lg:col-span-4 row-span-1"
            >
              {renderEarningsCard()}
            </motion.div>

            {/* Watchlist - Colorful Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="col-span-12 lg:col-span-6 row-span-1"
            >
              {renderWatchlistCard()}
            </motion.div>

            {/* Recent Activity - Square Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="col-span-12 lg:col-span-6 row-span-1"
            >
              {renderRecentActivityCard()}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
