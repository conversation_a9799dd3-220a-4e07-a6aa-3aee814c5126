import React, { useState, useEffect, useRef } from 'react';
import { User, Eye, EyeOff, Upload, LogOut, DollarSign } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useResponsive } from '@/hooks/useResponsive';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';
import SellerDashboard from '@/components/marketplace/SellerDashboard';
import PurchasedAgents from '@/components/marketplace/PurchasedAgents';
import ErrorBoundary from '@/components/ErrorBoundary';
import { debugSettings } from '@/utils/settingsDebug';

const Settings = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { isMobile, classes } = useResponsive();
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [avatar, setAvatar] = useState('');
  const [loading, setLoading] = useState(false);
  const [uploadingAvatar, setUploadingAvatar] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [activeSection, setActiveSection] = useState('profile');
  const [hasChanges, setHasChanges] = useState(false);
  const [originalFullName, setOriginalFullName] = useState('');
  const [originalAvatar, setOriginalAvatar] = useState('');

  // Password change states
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [saveTimeout, setSaveTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    debugSettings.logComponentLifecycle('Settings', 'mount');

    const fetchUser = async () => {
      const monitor = debugSettings.monitorSaveOperation('User Data Fetch');
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          setUser(user);
          setEmail(user.email || '');

          // Fetch profile data if available
          const { data: profile } = await supabase
            .from('profiles')
            .select('full_name, avatar_url')
            .eq('id', user.id)
            .single();

          if (profile) {
            setFullName(profile.full_name || '');
            setOriginalFullName(profile.full_name || '');
            setAvatar(profile.avatar_url || '');
            setOriginalAvatar(profile.avatar_url || '');
          }
        }
        monitor.end(true);
      } catch (error) {
        monitor.end(false);
        console.error('Error fetching user data:', error);
      }
    };

    fetchUser();

    return () => {
      debugSettings.logComponentLifecycle('Settings', 'unmount');
    };
  }, []);

  // Track changes
  useEffect(() => {
    setHasChanges(fullName !== originalFullName || avatar !== originalAvatar);
  }, [fullName, originalFullName, avatar, originalAvatar]);

  // Password strength calculator
  const calculatePasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength += 25;
    if (password.match(/[a-z]/)) strength += 25;
    if (password.match(/[A-Z]/)) strength += 25;
    if (password.match(/[0-9]/)) strength += 25;
    return strength;
  };

  useEffect(() => {
    setPasswordStrength(calculatePasswordStrength(newPassword));
  }, [newPassword]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (saveTimeout) {
        clearTimeout(saveTimeout);
      }
    };
  }, [saveTimeout]);

  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid File",
        description: "Please select an image file.",
        variant: "destructive",
      });
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File Too Large",
        description: "Please select an image smaller than 5MB.",
        variant: "destructive",
      });
      return;
    }

    setUploadingAvatar(true);
    try {
      // Upload to Supabase storage with user folder structure
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}/${Date.now()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(fileName, file, {
          upsert: true
        });

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(fileName);

      setAvatar(publicUrl);

      toast({
        title: "Avatar Uploaded",
        description: "Your profile picture has been updated.",
      });
    } catch (error: any) {
      toast({
        title: "Upload Failed",
        description: error.message || "Failed to upload avatar. Please try again.",
        variant: "destructive",
      });
    } finally {
      setUploadingAvatar(false);
    }
  };

  const handleSaveChanges = async () => {
    if (loading) {
      console.log('🚫 Save blocked: Already saving');
      return; // Prevent multiple saves
    }

    const monitor = debugSettings.monitorSaveOperation('Profile Save');

    // Clear any existing timeout
    if (saveTimeout) {
      clearTimeout(saveTimeout);
      setSaveTimeout(null);
    }

    setLoading(true);
    try {
      console.log('💾 Starting profile save...', { fullName, avatar: !!avatar });

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Save operation timed out')), 10000)
      );

      // Update profile in database with timeout
      const savePromise = supabase
        .from('profiles')
        .upsert({
          id: user.id,
          full_name: fullName,
          avatar_url: avatar,
          updated_at: new Date()
        });

      const { error } = await Promise.race([savePromise, timeoutPromise]) as any;

      if (error) throw error;

      setOriginalFullName(fullName);
      setOriginalAvatar(avatar);
      setHasChanges(false);

      console.log('✅ Profile saved successfully');
      monitor.end(true);

      toast({
        title: "Profile Updated",
        description: "Your profile has been successfully updated.",
        duration: 3000,
      });
    } catch (error: any) {
      console.error('❌ Error saving profile:', error);
      monitor.end(false);

      toast({
        title: "Error",
        description: error.message === 'Save operation timed out'
          ? "Save operation timed out. Please check your connection and try again."
          : error.message || "Failed to update profile. Please try again.",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setLoading(false);
      console.log('🏁 Save operation completed');
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    setPasswordError('');

    if (newPassword !== confirmPassword) {
      setPasswordError("New passwords don't match");
      return;
    }

    if (newPassword.length < 8) {
      setPasswordError("Password must be at least 8 characters");
      return;
    }

    if (passwordStrength < 75) {
      setPasswordError('Password is too weak. Please include uppercase, lowercase, and numbers.');
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) throw error;

      // Reset form and show success
      setNewPassword('');
      setConfirmPassword('');
      setShowPasswordForm(false);

      toast({
        title: "Password Updated",
        description: "Your password has been successfully changed.",
        duration: 3000,
      });
    } catch (error: any) {
      setPasswordError(error.message || "Failed to change password");
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      toast({
        title: "Signed Out",
        description: "You have been successfully signed out.",
        duration: 3000,
      });

      // Navigate to home page
      navigate('/');
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to sign out. Please try again.",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`h-full bg-[#0A0A0A] ${isMobile ? 'px-3 py-3' : classes.container}`}>
      <div className={`${isMobile ? 'max-w-full px-3' : 'max-w-lg'} mx-auto py-4`}>

        {/* Simple Section Tabs */}
        <div className="flex gap-1 mb-8 p-1 bg-white/[0.02] rounded-lg border border-white/[0.04]">
          <button
            onClick={() => setActiveSection('profile')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-sans transition-all duration-200 ${
              activeSection === 'profile'
                ? 'bg-white/[0.08] text-white border border-white/[0.08]'
                : 'text-white/60 hover:text-white/80'
            }`}
          >
            Profile
          </button>
          <button
            onClick={() => setActiveSection('marketplace')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-sans transition-all duration-200 ${
              activeSection === 'marketplace'
                ? 'bg-white/[0.08] text-white border border-white/[0.08]'
                : 'text-white/60 hover:text-white/80'
            }`}
          >
            <DollarSign className="w-4 h-4 inline mr-1" />
            Marketplace
          </button>
          <button
            onClick={() => setActiveSection('security')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-sans transition-all duration-200 ${
              activeSection === 'security'
                ? 'bg-white/[0.08] text-white border border-white/[0.08]'
                : 'text-white/60 hover:text-white/80'
            }`}
          >
            Security
          </button>
        </div>

        {/* Content Area */}
        <div className="space-y-6">

          {activeSection === 'profile' && (
            <div className="space-y-6">
              {/* Profile Picture */}
              <div className="space-y-3">
                <label className="text-sm text-white/70 font-sans">Profile Picture</label>
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <div className="w-16 h-16 rounded-full overflow-hidden bg-white/[0.05] border border-white/[0.08] flex items-center justify-center">
                      {avatar ? (
                        <img
                          src={avatar}
                          alt="Profile"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <User className="w-6 h-6 text-white/40" />
                      )}
                    </div>
                    {uploadingAvatar && (
                      <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleAvatarUpload}
                      className="hidden"
                    />
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      disabled={uploadingAvatar}
                      className="flex items-center gap-2 px-4 py-2 bg-white/[0.05] hover:bg-white/[0.08] border border-white/[0.08] rounded-lg text-white/70 hover:text-white transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed font-sans"
                    >
                      <Upload className="w-4 h-4" />
                      {uploadingAvatar ? 'Uploading...' : 'Upload Photo'}
                    </button>
                    <p className="text-xs text-white/40 mt-2 font-sans">
                      JPG, PNG or GIF. Max size 5MB.
                    </p>
                  </div>
                </div>
              </div>

              {/* Full Name */}
              <div className="space-y-2">
                <label className="text-sm text-white/70 font-sans">Full Name</label>
                <Input
                  type="text"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  className="bg-transparent border border-white/20 rounded-lg text-white placeholder:text-white/40 focus:border-white/40 focus:ring-0 transition-colors duration-200 font-sans"
                  placeholder="Enter your full name"
                />
              </div>

              {/* Email */}
              <div className="space-y-2">
                <label className="text-sm text-white/70 font-sans">Email Address</label>
                <Input
                  type="email"
                  value={email}
                  disabled
                  className="bg-transparent border border-white/10 rounded-lg text-white/50 cursor-not-allowed font-sans"
                />
                <p className="text-xs text-white/40 font-sans">Email cannot be changed</p>
              </div>

              {/* Save Button */}
              <button
                onClick={handleSaveChanges}
                disabled={loading || !hasChanges}
                className={`w-full py-2.5 rounded-lg text-sm font-medium transition-all duration-200 font-sans ${
                  hasChanges && !loading
                    ? 'bg-white hover:bg-gray-50 text-black shadow-[inset_0_1px_2px_rgba(0,0,0,0.05),0_1px_3px_rgba(0,0,0,0.1)]'
                    : 'bg-white/[0.03] text-white/40 cursor-not-allowed border border-white/[0.08]'
                }`}
              >
                {loading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin inline-block mr-2" />
                    Saving...
                  </>
                ) : hasChanges ? (
                  'Save Changes'
                ) : (
                  'Saved'
                )}
              </button>

              {loading && (
                <p className="text-xs text-white/50 text-center mt-2 font-sans">
                  Please wait while we save your changes...
                </p>
              )}
            </div>
          )}

          {activeSection === 'marketplace' && (
            <div className="space-y-6">
              {/* Marketplace Tabs */}
              <div className="flex gap-1 p-1 bg-white/[0.02] rounded-lg border border-white/[0.04]">
                <button
                  onClick={() => setActiveSection('marketplace')}
                  className="flex-1 py-2 px-4 rounded-md text-sm font-sans bg-white/[0.08] text-white border border-white/[0.08]"
                >
                  💰 My Earnings
                </button>
                <button
                  onClick={() => setActiveSection('purchased')}
                  className="flex-1 py-2 px-4 rounded-md text-sm font-sans text-white/60 hover:text-white/80 transition-all duration-200"
                >
                  🛒 Purchased Agents
                </button>
              </div>

              {/* Seller Dashboard */}
              <ErrorBoundary
                fallback={
                  <div className="text-center p-8">
                    <p className="text-white/60">Unable to load seller dashboard</p>
                    <button
                      onClick={() => window.location.reload()}
                      className="mt-2 text-blue-400 hover:text-blue-300"
                    >
                      Refresh page
                    </button>
                  </div>
                }
              >
                <SellerDashboard />
              </ErrorBoundary>
            </div>
          )}

          {activeSection === 'purchased' && (
            <div className="space-y-6">
              {/* Marketplace Tabs */}
              <div className="flex gap-1 p-1 bg-white/[0.02] rounded-lg border border-white/[0.04]">
                <button
                  onClick={() => setActiveSection('marketplace')}
                  className="flex-1 py-2 px-4 rounded-md text-sm font-sans text-white/60 hover:text-white/80 transition-all duration-200"
                >
                  💰 My Earnings
                </button>
                <button
                  onClick={() => setActiveSection('purchased')}
                  className="flex-1 py-2 px-4 rounded-md text-sm font-sans bg-white/[0.08] text-white border border-white/[0.08]"
                >
                  🛒 Purchased Agents
                </button>
              </div>

              {/* Purchased Agents */}
              <ErrorBoundary
                fallback={
                  <div className="text-center p-8">
                    <p className="text-white/60">Unable to load purchased agents</p>
                    <button
                      onClick={() => window.location.reload()}
                      className="mt-2 text-blue-400 hover:text-blue-300"
                    >
                      Refresh page
                    </button>
                  </div>
                }
              >
                <PurchasedAgents />
              </ErrorBoundary>
            </div>
          )}

          {activeSection === 'security' && (
            <div className="space-y-6">
              {!showPasswordForm ? (
                <>
                  <p className="text-white/50 text-sm font-sans">Manage your account security and password</p>

                  <button
                    onClick={() => setShowPasswordForm(true)}
                    className="w-full py-2.5 rounded-lg text-sm bg-white/[0.03] hover:bg-white/[0.06] text-white/90 border border-white/[0.08] transition-colors duration-200 font-sans"
                  >
                    Change Password
                  </button>

                  {/* Sign Out Button */}
                  <div className="pt-4 border-t border-white/[0.08]">
                    <button
                      onClick={handleSignOut}
                      disabled={loading}
                      className="w-full flex items-center justify-center gap-2 py-2.5 rounded-lg text-sm bg-red-500/10 hover:bg-red-500/20 text-red-400 hover:text-red-300 border border-red-500/20 transition-colors duration-200 font-sans disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <LogOut className="w-4 h-4" />
                      {loading ? 'Signing Out...' : 'Sign Out'}
                    </button>
                  </div>
                </>
              ) : (
                <form onSubmit={handlePasswordChange} className="space-y-4">
                  {/* New Password */}
                  <div className="space-y-2">
                    <label className="text-sm text-white/70 font-sans">New Password</label>
                    <div className="relative">
                      <Input
                        type={showNewPassword ? "text" : "password"}
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        className="bg-transparent border border-white/20 rounded-lg text-white placeholder:text-white/40 focus:border-white/40 focus:ring-0 transition-colors duration-200 pr-10 font-sans"
                        placeholder="Enter new password"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/40 hover:text-white/60 transition-colors"
                      >
                        {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>

                    {/* Password Strength */}
                    {newPassword && (
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span className="text-white/50 font-sans">Strength</span>
                          <span className={`font-sans ${
                            passwordStrength >= 75 ? 'text-emerald-400' :
                            passwordStrength >= 50 ? 'text-yellow-400' : 'text-red-400'
                          }`}>
                            {passwordStrength >= 75 ? 'Strong' :
                             passwordStrength >= 50 ? 'Medium' : 'Weak'}
                          </span>
                        </div>
                        <div className="w-full bg-white/10 rounded-full h-1">
                          <div
                            className={`h-1 rounded-full transition-all duration-300 ${
                              passwordStrength >= 75 ? 'bg-emerald-500' :
                              passwordStrength >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${passwordStrength}%` }}
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Confirm Password */}
                  <div className="space-y-2">
                    <label className="text-sm text-white/70 font-sans">Confirm Password</label>
                    <div className="relative">
                      <Input
                        type={showConfirmPassword ? "text" : "password"}
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        className="bg-transparent border border-white/20 rounded-lg text-white placeholder:text-white/40 focus:border-white/40 focus:ring-0 transition-colors duration-200 pr-10 font-sans"
                        placeholder="Confirm new password"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/40 hover:text-white/60 transition-colors"
                      >
                        {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  {passwordError && (
                    <p className="text-red-400 text-sm font-sans">{passwordError}</p>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-3 pt-2">
                    <button
                      type="button"
                      onClick={() => {
                        setShowPasswordForm(false);
                        setPasswordError('');
                        setNewPassword('');
                        setConfirmPassword('');
                      }}
                      className="flex-1 py-2.5 rounded-lg text-sm bg-white/[0.03] hover:bg-white/[0.06] text-white/70 border border-white/[0.08] transition-colors duration-200 font-sans"
                    >
                      Cancel
                    </button>

                    <button
                      type="submit"
                      disabled={loading || passwordStrength < 75}
                      className={`flex-1 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 font-sans ${
                        passwordStrength >= 75 && newPassword === confirmPassword && newPassword.length >= 8
                          ? 'bg-white hover:bg-gray-50 text-black shadow-[inset_0_1px_2px_rgba(0,0,0,0.05),0_1px_3px_rgba(0,0,0,0.1)]'
                          : 'bg-white/[0.03] text-white/40 cursor-not-allowed border border-white/[0.08]'
                      }`}
                    >
                      {loading ? (
                        <>
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin inline-block mr-2" />
                          Updating...
                        </>
                      ) : (
                        'Update Password'
                      )}
                    </button>
                  </div>
                </form>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;
