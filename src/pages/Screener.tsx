import React from 'react';
import { useParams } from 'react-router-dom';
import TradingChart from '@/components/TradingChart';

const Screener: React.FC = () => {
  const { symbol } = useParams<{ symbol?: string }>();
  const displaySymbol = symbol || 'TSLA';

  return (
    <div className="h-full bg-[#0A0A0A] overflow-hidden">
      <div className="h-full w-full">
        <TradingChart symbol={displaySymbol} />
      </div>
    </div>
  );
};

export default Screener;
