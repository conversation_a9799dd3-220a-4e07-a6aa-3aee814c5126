import React, { useEffect, useState } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from '@/contexts/AuthContext';

const PostAuthHandler = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [hasCheckedSubscription, setHasCheckedSubscription] = useState(false);

  useEffect(() => {
    const checkSubscriptionStatus = async () => {
      if (!user || hasCheckedSubscription) {
        setIsLoading(false);
        return;
      }

      try {
        // First check the subscriptions table
        const { data: subscriptionData, error: subscriptionError } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle();

        if (subscriptionError) {
          console.error('Error checking subscription:', subscriptionError);
        } else {
          // Check if user has an active subscription in the subscriptions table
          if (subscriptionData &&
              subscriptionData.status &&
              ['active', 'trialing'].includes(subscriptionData.status)) {
            console.log('✅ PostAuthHandler: User has active subscription', subscriptionData);
            setIsLoading(false);
            setHasCheckedSubscription(true);
            return;
          }
        }

        // If no active subscription found in subscriptions table, check subscription_mappings
        // Get user's email from session
        const userEmail = user.email;

        if (userEmail) {
          const { data: mappingData, error: mappingError } = await supabase
            .from('subscription_mappings')
            .select('*')
            .eq('email', userEmail)
            .maybeSingle();

          if (mappingError) {
            console.error('Error checking subscription_mappings:', mappingError);
          } else if (mappingData) {
            // If there's any entry for this email in subscription_mappings, consider them paid
            setIsLoading(false);
            setHasCheckedSubscription(true);
            return;
          }
        }

        // If we get here, the user is not already paying - but we no longer show waitlist
        // Just let them through to the normal app flow
        setIsLoading(false);
        setHasCheckedSubscription(true);
      } catch (error) {
        console.error('Error checking subscription status:', error);
        setIsLoading(false);
        setHasCheckedSubscription(true);
      }
    };

    checkSubscriptionStatus();
  }, [user, hasCheckedSubscription]);

  // If still loading, show a loading spinner
  if (isLoading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#0EA5E9]"></div>
      </div>
    );
  }

  // Always let users through - no more waitlist
  return null;
};

export default PostAuthHandler;
