import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  <PERSON><PERSON><PERSON>,
  Settings,
  Plus
} from 'lucide-react';
import { cn } from '@/lib/utils';

const BottomNavigation: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Navigation items for bottom nav
  const navItems = [
    {
      id: 'home',
      label: 'Home',
      icon: <img
        src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/dashboard.svg"
        alt="Dashboard"
        className="w-5 h-5"
      />,
      path: '/home'
    },
    {
      id: 'portfolio-builder',
      label: 'Portfolio',
      icon: <PieChart className="w-5 h-5" />,
      path: '/portfolio-builder'
    },
    {
      id: 'discover',
      label: 'Discover',
      icon: <img
        src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons//glboe.svg"
        alt="Discover"
        className="w-5 h-5"
      />,
      path: '/discover'
    },
    {
      id: 'stock-screener',
      label: 'Screener',
      icon: <img
        src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/Search.svg"
        alt="Screener"
        className="w-5 h-5"
      />,
      path: '/stock-screener'
    },
    {
      id: 'agent-backtesting',
      label: 'Backtest',
      icon: <img
        src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/Bar%20chart.svg"
        alt="Bar Chart"
        className="w-5 h-5"
      />,
      path: '/agent-backtesting'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings className="w-5 h-5" />,
      path: '/settings'
    }
  ];

  const isActiveRoute = (path: string) => {
    if (path === '/home') {
      return location.pathname === '/' || location.pathname === '/home';
    }
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const handleNavigation = (item: any) => {
    // All pages are now accessible - navigate directly
    navigate(item.path);
  };

  return (
    <>
      {/* Floating Build Agent Button */}
      <button
        onClick={() => navigate('/agent-builder/new')}
        className="fixed bottom-24 right-4 z-50 w-14 h-14 bg-white text-black rounded-full flex items-center justify-center shadow-lg hover:bg-gray-100 transition-all duration-200"
        title="Build Agent"
      >
        <Plus className="w-6 h-6" />
      </button>

      {/* Enhanced Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 z-50">
        {/* Gradient overlay for seamless integration */}
        <div className="absolute inset-x-0 -top-8 h-8 bg-gradient-to-t from-[#0A0A0A] to-transparent pointer-events-none"></div>

        {/* Main navigation container */}
        <div className="bg-gradient-to-t from-[#0A0A0A] via-[#0F0F0F] to-[#141414] border-t border-white/[0.06] backdrop-blur-xl">
          {/* Subtle top highlight */}
          <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/[0.08] to-transparent"></div>

          <div className="flex items-center justify-around px-2 py-3 max-w-screen-xl mx-auto relative">
            {navItems.map((item) => (
              <button
                key={item.id}
                onClick={() => handleNavigation(item)}
                className={cn(
                  "group flex flex-col items-center gap-1 px-3 py-2 rounded-xl transition-all duration-300 min-w-0 flex-shrink-0 relative overflow-hidden focus:outline-none focus:ring-0",
                  isActiveRoute(item.path)
                    ? "bg-white/[0.08] text-white"
                    : "text-white/50 hover:text-white/80 hover:bg-white/[0.03]"
                )}
                title={item.label}
              >

                {/* Icon container with enhanced styling */}
                <div className={cn(
                  "flex-shrink-0 transition-all duration-300 relative",
                  isActiveRoute(item.path)
                    ? "text-white transform scale-110"
                    : "text-white/60 group-hover:text-white/90 group-hover:scale-105"
                )}>
                  {/* Subtle glow effect for active items */}
                  {isActiveRoute(item.path) && (
                    <div className="absolute inset-0 bg-white/10 rounded-lg blur-sm"></div>
                  )}
                  <div className="relative z-10">
                    {item.icon}
                  </div>
                </div>

                {/* Label with better typography */}
                <span className={cn(
                  "text-[11px] font-medium truncate text-center leading-tight transition-all duration-300",
                  isActiveRoute(item.path)
                    ? "text-white"
                    : "text-white/50 group-hover:text-white/80"
                )}
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                >
                  {item.label}
                </span>

                {/* Subtle hover effect */}
                <div className="absolute inset-0 bg-gradient-to-t from-white/[0.02] to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Enhanced styles for smooth animations and cohesive design */}
      <style>{`
        /* Remove all focus outlines and borders */
        button:focus,
        button:focus-visible,
        button:active {
          outline: none !important;
          border: none !important;
          box-shadow: none !important;
        }

        /* Smooth transitions for all navigation elements */
        .group {
          transition-property: all;
          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
          transition-duration: 300ms;
        }

        /* Enhanced backdrop blur for better integration */
        .backdrop-blur-xl {
          backdrop-filter: blur(24px) saturate(180%);
        }

        /* Smooth scale animations */
        .group:hover .group-hover\\:scale-105 {
          transform: scale(1.05);
        }

        .transform.scale-110 {
          transform: scale(1.1);
        }

        /* Remove any default button styling */
        button {
          -webkit-tap-highlight-color: transparent;
          -webkit-touch-callout: none;
          -webkit-user-select: none;
          user-select: none;
        }
      `}</style>
    </>
  );
};

export default BottomNavigation;
