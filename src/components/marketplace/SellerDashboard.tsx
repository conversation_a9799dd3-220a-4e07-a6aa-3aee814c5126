import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { 
  DollarSign, 
  TrendingUp, 
  ShoppingCart, 
  Calendar,
  User,
  ExternalLink,
  CreditCard,
  AlertCircle
} from 'lucide-react';
import { 
  getSellerEarnings, 
  getSellerAccount, 
  createSellerAccount, 
  createAccountLink,
  type SellerEarning,
  type EarningsSummary 
} from '@/services/marketplaceService';
import { formatDistanceToNow } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';

const SellerDashboard: React.FC = () => {
  const { toast } = useToast();
  const [earnings, setEarnings] = useState<SellerEarning[]>([]);
  const [summary, setSummary] = useState<EarningsSummary>({
    total_earnings: 0,
    available_earnings: 0,
    paid_out_earnings: 0,
    total_sales: 0
  });
  const [sellerAccount, setSellerAccount] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [settingUpAccount, setSettingUpAccount] = useState(false);
  const [loadingDashboard, setLoadingDashboard] = useState(false);

  const loadData = async () => {
    setLoading(true);
    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), 10000)
      );

      // Load earnings and seller account in parallel with timeout
      const [earningsResponse, accountResponse] = await Promise.race([
        Promise.all([
          getSellerEarnings(),
          getSellerAccount()
        ]),
        timeoutPromise
      ]) as [any, any];

      if (earningsResponse.success) {
        setEarnings(earningsResponse.earnings);
        setSummary(earningsResponse.summary);
      }

      console.log('Account response:', accountResponse);

      if (accountResponse.success && accountResponse.account) {
        setSellerAccount(accountResponse.account);
        console.log('Seller account loaded:', accountResponse.account);
      } else {
        console.log('No seller account found or error:', accountResponse.error);
      }
    } catch (error) {
      console.error('Error loading seller data:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message === 'Request timeout'
          ? "Request timed out. Please try again."
          : "Failed to load seller dashboard data"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let isMounted = true;

    const loadDataSafely = async () => {
      if (isMounted) {
        await loadData();
      }
    };

    loadDataSafely();

    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array is correct here

  const handleSetupAccount = async () => {
    setSettingUpAccount(true);
    try {
      // Get user email for account creation
      const { data: { user } } = await supabase.auth.getUser();
      if (!user?.email) {
        throw new Error('User email not found');
      }

      // First create the account if it doesn't exist
      let stripeAccountId = sellerAccount?.id;

      if (!sellerAccount) {
        console.log('Creating new seller account...');
        const createResponse = await createSellerAccount(user.email, 'US');
        if (!createResponse.success) {
          throw new Error(createResponse.error || 'Failed to create seller account');
        }
        stripeAccountId = createResponse.account_id;
        console.log('Created account with ID:', stripeAccountId);
      }

      if (!stripeAccountId) {
        throw new Error('No Stripe account ID available');
      }

      console.log('Creating account link for:', stripeAccountId);

      // Create account link for onboarding
      const linkResponse = await createAccountLink(
        stripeAccountId,
        `${window.location.origin}/settings?tab=marketplace&setup=refresh`,
        `${window.location.origin}/settings?tab=marketplace&setup=complete`
      );

      console.log('Account link response:', linkResponse);

      if (linkResponse.success && linkResponse.url) {
        // Redirect to Stripe onboarding
        console.log('Redirecting to:', linkResponse.url);
        window.location.href = linkResponse.url;
      } else {
        console.error('Account link failed:', linkResponse);
        throw new Error(linkResponse.error || 'Failed to create account link');
      }
    } catch (error) {
      console.error('Error setting up seller account:', error);
      toast({
        variant: "destructive",
        title: "Setup Error",
        description: error.message || "Failed to set up seller account"
      });
    } finally {
      setSettingUpAccount(false);
    }
  };

  const handleViewStripeDashboard = async () => {
    console.log('Seller account debug:', {
      sellerAccount,
      hasStripeAccountId: !!sellerAccount?.stripe_account_id,
      chargesEnabled: sellerAccount?.charges_enabled,
      accountStatus: sellerAccount?.account_status
    });

    if (!sellerAccount?.stripe_account_id) {
      toast({
        variant: "destructive",
        title: "No Seller Account",
        description: "Please complete your seller setup first."
      });
      return;
    }

    setLoadingDashboard(true);
    try {
      // Create Stripe Express Dashboard link
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/marketplace-stripe-connect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify({
          action: 'create-dashboard-link',
          account_id: sellerAccount.stripe_account_id
        })
      });

      const data = await response.json();

      if (data.success && data.url) {
        // Open Stripe Express Dashboard in new tab
        window.open(data.url, '_blank');
      } else {
        throw new Error(data.error || 'Failed to create dashboard link');
      }
    } catch (error) {
      console.error('Error creating dashboard link:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to open Stripe dashboard. Please try again."
      });
    } finally {
      setLoadingDashboard(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white/20 mx-auto mb-4"></div>
          <p className="text-white/50">Loading seller dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Account Status */}
      {!sellerAccount?.charges_enabled && (
        <Card className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 border-yellow-500/20">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <AlertCircle className="w-6 h-6 text-yellow-400 mt-1" />
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-white mb-2">
                  Complete Your Seller Setup
                </h3>
                <p className="text-white/70 mb-4">
                  To start selling agents and receive payments, you need to complete your seller account setup with Stripe Connect.
                </p>
                <Button
                  onClick={handleSetupAccount}
                  disabled={settingUpAccount}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white"
                >
                  {settingUpAccount ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Setting up...</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <CreditCard className="w-4 h-4" />
                      <span>Complete Setup</span>
                    </div>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Demo Account Success */}
      {sellerAccount?.charges_enabled && (
        <Card className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border-green-500/20">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <CreditCard className="w-6 h-6 text-green-400 mt-1" />
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-white mb-2">
                  ✅ Seller Account Active
                </h3>
                <p className="text-white/70">
                  Your seller account is set up and ready to receive payments!
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stripe Dashboard Access */}
      <Card className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border-blue-500/20">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
            <ExternalLink className="w-5 h-5 text-blue-400" />
            Seller Dashboard
          </CardTitle>
          <CardDescription className="text-white/60">
            View your earnings, sales, and payout information in Stripe's secure dashboard
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex-1">
              <h3 className="font-medium text-white mb-2">Access Your Stripe Express Dashboard</h3>
              <p className="text-sm text-white/60 mb-4">
                View detailed sales analytics, earnings history, payout schedules, and manage your account settings directly in Stripe's professional dashboard.
              </p>
              <div className="flex flex-wrap gap-2 text-xs text-white/50">
                <span>• Real-time earnings</span>
                <span>• Payout history</span>
                <span>• Tax documents</span>
                <span>• Account settings</span>
              </div>
            </div>
            <Button
              onClick={handleViewStripeDashboard}
              disabled={!sellerAccount?.charges_enabled || loadingDashboard}
              className="bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-lg min-w-[140px]"
            >
              {loadingDashboard ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Opening...</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <ExternalLink className="w-4 h-4" />
                  <span>Open Dashboard</span>
                </div>
              )}
            </Button>
          </div>

          {!sellerAccount?.charges_enabled && (
            <div className="mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
              <div className="flex items-center gap-2 text-yellow-400 text-sm">
                <AlertCircle className="w-4 h-4" />
                <span>Complete your account setup to access the dashboard</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Transactions */}
      <Card className="bg-gradient-to-br from-white/[0.03] to-white/[0.01] border-white/[0.08]">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-white">Recent Sales</CardTitle>
          <CardDescription className="text-white/60">
            Your latest agent sales and earnings
          </CardDescription>
        </CardHeader>
        <CardContent>
          {earnings.length === 0 ? (
            <div className="text-center py-8">
              <ShoppingCart className="w-12 h-12 text-white/20 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white/60 mb-2">No sales yet</h3>
              <p className="text-white/40">
                Start selling your agents to see earnings here
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {earnings.slice(0, 10).map((earning) => (
                <div
                  key={earning.id}
                  className="flex items-center justify-between p-4 bg-white/[0.02] border border-white/[0.05] rounded-lg"
                >
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-green-500/20 to-blue-500/20 rounded-lg flex items-center justify-center">
                      <DollarSign className="w-5 h-5 text-green-400" />
                    </div>
                    <div>
                      <h4 className="font-medium text-white">
                        {earning.transaction?.agents?.name || 'Unknown Agent'}
                      </h4>
                      <div className="flex items-center gap-2 text-sm text-white/60">
                        <User className="w-3 h-3" />
                        <span>{earning.transaction?.buyer?.full_name || 'Anonymous'}</span>
                        <span>•</span>
                        <Calendar className="w-3 h-3" />
                        <span>{formatDistanceToNow(new Date(earning.created_at), { addSuffix: true })}</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-white">
                      {formatCurrency(earning.amount)}
                    </div>
                    <Badge
                      variant={earning.status === 'paid_out' ? 'default' : 'secondary'}
                      className={
                        earning.status === 'paid_out'
                          ? 'bg-green-500/20 text-green-400'
                          : earning.status === 'available'
                          ? 'bg-blue-500/20 text-blue-400'
                          : 'bg-yellow-500/20 text-yellow-400'
                      }
                    >
                      {earning.status === 'paid_out' ? 'Paid' : 
                       earning.status === 'available' ? 'Available' : 'Pending'}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SellerDashboard;
