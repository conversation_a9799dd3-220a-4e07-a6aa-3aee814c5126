import { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import ReactECharts from 'echarts-for-react';
import {
  FaChartBar, FaRegBell, <PERSON>a<PERSON>og, <PERSON>a<PERSON><PERSON><PERSON>, Fa<PERSON>hart<PERSON><PERSON>, Fa<PERSON><PERSON>er, FaReg<PERSON><PERSON>bulb
} from 'react-icons/fa';
import ChartSettingsModal from './ChartSettingsModal';
import './TradingChart.css';
import { fetchStockData, getDateRangeForLastDays, convertToChartData } from '../../services/auraPolygonService';
import {
  ChartBounds,
  calculateChartBounds,
  storeDrawingWithBothCoordinates,
  updateDrawingsForNewTimeframe,
  convertDrawingToAbsolute,
  convertDrawingToRelative
} from '../../utils/drawingCoordinates';
import {
  saveDrawingsForSymbol,
  loadDrawingsForSymbol,
  autoSaveDrawings,
  clearDrawingsForSymbol
} from '../../utils/drawingPersistence';

// Define the CandlestickData type
interface CandlestickData {
  time: string | number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
}



// Generate more realistic trading data with hourly timestamps
const generateHourlyData = (days: number): CandlestickData[] => {
  const data: CandlestickData[] = [];
  let basePrice = 0.00953;
  const now = new Date();

  // Create a more realistic price pattern with trends and volatility clusters
  const trendDirections = [1, -1, 1, -1, 1]; // Alternating trends
  const trendLengths = [12, 18, 24, 16, 20]; // Hours per trend

  let currentTrendIndex = 0;
  let hoursInCurrentTrend = 0;
  let trendDirection = trendDirections[0];
  let volatilityMultiplier = 1;

  for (let i = days * 24; i >= 0; i--) {
    // Check if we need to switch trends
    if (hoursInCurrentTrend >= trendLengths[currentTrendIndex]) {
      currentTrendIndex = (currentTrendIndex + 1) % trendDirections.length;
      trendDirection = trendDirections[currentTrendIndex];
      hoursInCurrentTrend = 0;
      volatilityMultiplier = 0.8 + Math.random() * 0.8; // Random volatility between trends
    }

    const date = new Date(now.getTime() - i * 60 * 60 * 1000);
    const baseVolatility = 0.00015 * volatilityMultiplier;
    const trendStrength = 0.00005 * trendDirection;

    // Add some randomness to the trend
    const randomFactor = (Math.random() - 0.5) * 0.00008;

    const open = basePrice;
    const close = basePrice + trendStrength + randomFactor;

    // Higher volatility for high/low
    const highVolatility = baseVolatility * (1 + Math.random() * 0.5);
    const lowVolatility = baseVolatility * (1 + Math.random() * 0.5);

    const high = Math.max(open, close) + highVolatility;
    const low = Math.min(open, close) - lowVolatility;

    basePrice = close;
    hoursInCurrentTrend++;

    data.push({
      time: Math.floor(date.getTime() / 1000) as any,
      open,
      high,
      low,
      close
    });
  }

  return data;
};

// Generate candle data that matches the selected timeframe
const generateCandleDataForTimeframe = (timeframe: string): CandlestickData[] => {
  const data: CandlestickData[] = [];
  let basePrice = 0.00953;
  const now = new Date();

  // Determine candle interval and number of candles
  let intervalMs: number;
  let numCandles: number;

  switch (timeframe) {
    case '1m':
      intervalMs = 60 * 1000; // 1 minute
      numCandles = 7 * 24 * 60; // 7 days worth of 1-minute candles (10,080 candles)
      break;
    case '5m':
      intervalMs = 5 * 60 * 1000; // 5 minutes
      numCandles = 7 * 24 * 12; // 7 days worth of 5-minute candles (2,016 candles)
      break;
    case '15m':
      intervalMs = 15 * 60 * 1000; // 15 minutes
      numCandles = 7 * 24 * 4; // 7 days worth of 15-minute candles (672 candles)
      break;
    case '1h':
      intervalMs = 60 * 60 * 1000; // 1 hour
      numCandles = 43800; // 5 years worth of hourly candles
      break;
    case '4h':
      intervalMs = 4 * 60 * 60 * 1000; // 4 hours
      numCandles = 10950; // 5 years worth of 4-hour candles
      break;
    case '1d':
      intervalMs = 24 * 60 * 60 * 1000; // 1 day
      numCandles = 1825; // 5 years worth of daily candles
      break;
    case '1M':
      intervalMs = 30 * 24 * 60 * 60 * 1000; // ~1 month
      numCandles = 60; // 5 years worth
      break;
    case '1Y':
      intervalMs = 365 * 24 * 60 * 60 * 1000; // 1 year
      numCandles = 20; // 20 years worth
      break;
    default:
      intervalMs = 60 * 60 * 1000; // 1 hour default
      numCandles = 43800; // 5 years default
  }

  // Generate candles going backwards from now
  for (let i = numCandles; i >= 0; i--) {
    const time = new Date(now.getTime() - i * intervalMs);

    // Create realistic price movements based on timeframe
    const volatilityMultiplier = timeframe === '1m' ? 0.005 :
                                timeframe === '5m' ? 0.008 :
                                timeframe === '15m' ? 0.012 :
                                timeframe === '1h' ? 0.02 :
                                timeframe === '4h' ? 0.035 :
                                timeframe === '1d' ? 0.05 :
                                timeframe === '1M' ? 0.15 : 0.3;

    const trend = Math.sin(i / (numCandles / 10)) * 0.001;
    const randomChange = (Math.random() - 0.5) * volatilityMultiplier;

    basePrice = Math.max(0.001, basePrice * (1 + trend + randomChange));

    const open = basePrice;
    const close = open * (1 + (Math.random() - 0.5) * volatilityMultiplier * 0.5);
    const high = Math.max(open, close) * (1 + Math.random() * volatilityMultiplier * 0.3);
    const low = Math.min(open, close) * (1 - Math.random() * volatilityMultiplier * 0.3);

    data.push({
      time: time.toISOString(),
      open: parseFloat(open.toFixed(6)),
      high: parseFloat(high.toFixed(6)),
      low: parseFloat(low.toFixed(6)),
      close: parseFloat(close.toFixed(6)),
      volume: Math.floor(Math.random() * 1000000) + 100000
    });
  }

  return data.reverse();
};

interface TradingChartProps {
  // Add props as needed
  symbol?: string;
}

const TradingChart: React.FC<TradingChartProps> = ({ symbol = 'TSLA' }) => {
  const [activeTimeframe, setActiveTimeframe] = useState<string>('4h');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [stockData, setStockData] = useState<CandlestickData[]>([]);
  const [currentSymbol, setCurrentSymbol] = useState<string>(symbol);

  // State to preserve last valid chart data when invalid ticker is entered
  const [lastValidSymbol, setLastValidSymbol] = useState<string>(symbol);
  const [lastValidData, setLastValidData] = useState<CandlestickData[]>([]);
  const [lastValidPriceInfo, setLastValidPriceInfo] = useState({
    open: 0,
    high: 0,
    low: 0,
    close: 0,
    volume: 0
  });


  // Filter states for UI
  const [breakoutEnabled, setBreakoutEnabled] = useState<boolean>(false);
  const [rangeEnabled, setRangeEnabled] = useState<boolean>(false);
  const [trendEnabled, setTrendEnabled] = useState<boolean>(false);

  // Drawing tools states
  const [showDrawingTools] = useState<boolean>(true); // Always show sidebar
  const [activeTool, setActiveTool] = useState<string | null>(null);
  const [drawings, setDrawings] = useState<any[]>([]);
  const [isDrawingMode, setIsDrawingMode] = useState<boolean>(false);
  const [showShortcuts, setShowShortcuts] = useState<boolean>(false);
  const [currentDrawing, setCurrentDrawing] = useState<any>(null);
  const [isDrawing, setIsDrawing] = useState<boolean>(false);
  const [drawingLocked, setDrawingLocked] = useState<boolean>(false); // Prevents all chart updates during drawing
  const [recentlyUnlocked, setRecentlyUnlocked] = useState<boolean>(false); // Prevents immediate auto-scaling after unlock
  const [preventZoomReset, setPreventZoomReset] = useState<boolean>(false); // Prevents zoom reset during state changes
  const chartRef = useRef<any>(null);

  // Centralized state reset function to prevent chart freezing
  const resetDrawingStates = useCallback((reason: string = 'manual reset', preserveZoom: boolean = true) => {
    console.log(`🔄 Resetting drawing states - Reason: ${reason}, Preserve zoom: ${preserveZoom}`);

    // Store current zoom level BEFORE changing any states to prevent jump
    let currentZoom = null;
    if (preserveZoom && chartRef.current) {
      const zoomOption = chartRef.current.getOption()?.dataZoom?.[0];
      if (zoomOption && typeof zoomOption.start === 'number' && typeof zoomOption.end === 'number') {
        currentZoom = {
          start: zoomOption.start,
          end: zoomOption.end
        };
        console.log('📊 Captured zoom level:', currentZoom);
      }
    }

    // Capture current states before changing them
    const wasDrawingMode = isDrawingMode;
    const wasActiveTool = activeTool;

    if (preserveZoom && currentZoom) {
      setPreventZoomReset(true);
    }

    setActiveTool(null);
    setIsDrawingMode(false);
    setCurrentDrawing(null);
    setIsDrawing(false);
    setDrawingLocked(false);
    setRecentlyUnlocked(false);

    // Immediately restore zoom level to prevent any jumping
    if (currentZoom && chartRef.current && (wasDrawingMode || wasActiveTool)) {
      // Use requestAnimationFrame for immediate execution
      requestAnimationFrame(() => {
        if (chartRef.current) {
          chartRef.current.dispatchAction({
            type: 'dataZoom',
            start: currentZoom.start,
            end: currentZoom.end
          });
          console.log('✅ Zoom level immediately restored - no jump');

          // Clear the prevention flag after zoom is restored
          setTimeout(() => {
            setPreventZoomReset(false);
          }, 100);
        }
      });
    } else if (preserveZoom) {
      // Clear the flag even if we didn't need to restore zoom
      setTimeout(() => {
        setPreventZoomReset(false);
      }, 100);
    }
  }, []); // Remove dependencies to prevent recreation when states change

  // Removed aggressive state consistency check - it was causing unnecessary resets and interference

  // Settings modal state
  const [showSettingsModal, setShowSettingsModal] = useState<boolean>(false);

  // Load settings from localStorage or use defaults
  const getDefaultSettings = () => {
    const savedSettings = localStorage.getItem('tradingChartSettings');
    if (savedSettings) {
      try {
        return JSON.parse(savedSettings);
      } catch (error) {
        console.error('Error parsing saved settings:', error);
      }
    }

    return {
      bullishCandleColor: '#00e7b6',
      bearishCandleColor: '#ff4757',
      wickColor: '#ffffff',
      backgroundColor: '#0A0A0C',
      gridColor: 'rgba(255, 255, 255, 0.03)',
      textColor: 'rgba(255, 255, 255, 0.8)',
      chartType: 'candle' as 'candle' | 'line' | 'area' | 'hollow_candle' | 'heikin_ashi',
      showVolume: true,
      showGrid: true,
      showCrosshair: true,
      priceScaleMode: 'normal' as 'normal' | 'logarithmic' | 'percentage',
      autoScale: true,
      timeFormat: '24h' as '12h' | '24h',
      timezone: 'UTC',
      defaultLineColor: '#00e7b6',
      defaultLineWidth: 2,
      snapToPrice: true,
      maxDataPoints: 1000,
      enableAnimations: true
    };
  };

  const [chartSettings, setChartSettings] = useState(getDefaultSettings());





  // Re-render chart when settings change
  useEffect(() => {
    // Don't do anything if we're drawing - let the drawing complete first
    if (isDrawing || currentDrawing || drawingLocked) {
      console.log('❌ Chart settings update skipped: drawing in progress');
      return;
    }

    if (chartRef.current) {
      const newOption = getChartOptions();
      chartRef.current.setOption(newOption, {
        notMerge: false,
        lazyUpdate: false
      });
    }
  }, [chartSettings]);



  // Store chart bounds for drawing coordinate conversion
  const [chartBounds, setChartBounds] = useState<ChartBounds | null>(null);

  const [priceInfo, setPriceInfo] = useState({
    open: 0,
    high: 0,
    low: 0,
    close: 0,
    volume: 0
  });

  // TradingView-style drawing tools with exact Supabase icons
  const drawingTools = [
    {
      id: 'cursor',
      name: 'Cursor',
      icon: 'https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/sidebar/Group.png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzaWRlYmFyL0dyb3VwLnBuZyIsImlhdCI6MTc1MDE4NDA2NywiZXhwIjoxNzgxNzIwMDY3fQ.J29hT-j89Uexo_1hfbx2RgOaTeKmFkZW-0hrbBSIHak',
      iconType: 'image'
    },
    {
      id: 'line',
      name: 'Line',
      icon: 'https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/sidebar/Tool%20(3).png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzaWRlYmFyL1Rvb2wgKDMpLnBuZyIsImlhdCI6MTc1MDE4NDA5NywiZXhwIjoxNzgxNzIwMDk3fQ.PM9v4Xgs10pElrxUt8TVvgO4gn24q1pzHrmPXlGwYf8',
      iconType: 'image'
    },
    {
      id: 'circle',
      name: 'Circle',
      icon: '○',
      iconType: 'text'
    },
    {
      id: 'rectangle',
      name: 'Rectangle',
      icon: '□',
      iconType: 'text'
    },
    {
      id: 'measure',
      name: 'Measure',
      icon: 'https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/sidebar/Tool.png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzaWRlYmFyL1Rvb2wucG5nIiwiaWF0IjoxNzUwMTg0MTMwLCJleHAiOjE3ODE3MjAxMzB9.um_xvfGo9HkhX6CsvDnaW6qr7Zbh_HyBd0O3jA9_Rs4',
      iconType: 'image'
    },

    {
      id: 'delete',
      name: 'Delete',
      icon: '🗑️',
      iconType: 'text'
    },
    {
      id: 'clear',
      name: 'Clear All',
      icon: '🧹',
      iconType: 'text'
    }
  ];

  const handleToolSelect = (toolId: string) => {
    console.log(`🔧 Tool selected: ${toolId} - ISOLATED from zoom functionality`);

    // Handle special tools that need immediate action
    switch (toolId) {
      case 'clear':
        if (drawings.length > 0) {
          const confirmed = window.confirm(`Remove all ${drawings.length} drawing(s)?`);
          if (confirmed) {
            setDrawings([]);
            console.log('🗑️ All drawings cleared');
          }
        } else {
          console.log('No drawings to clear');
        }
        return; // Don't set activeTool for clear action

      case 'cursor':
        // Only reset if we're currently drawing something
        if (isDrawing || currentDrawing) {
          resetDrawingStates('switching to cursor', true);
        } else {
          // Just clear the tool state without any zoom effects
          setActiveTool(null);
          setIsDrawingMode(false);
        }
        return; // Exit early to prevent setting cursor as active tool

      case 'delete':
        console.log('🗑️ Delete mode activated - click on drawings to delete them');
        break;

      default:
        // Regular drawing tool - just log it
        const toolName = drawingTools.find(t => t.id === toolId)?.name || toolId;
        console.log(`🎨 Drawing tool selected: ${toolName}`);
        break;
    }

    // ISOLATED tool state changes - no zoom correlation
    setActiveTool(toolId);
    setIsDrawingMode(toolId !== 'cursor' && toolId !== 'delete');

    console.log(`✅ Tool selection complete: ${toolId} - No zoom effects triggered`);
  };

  // Simple delete function that can be called directly
  const deleteDrawing = (index: number) => {
    if (index >= 0 && index < drawings.length) {
      const drawingType = drawings[index].type;
      const confirmed = window.confirm(`Delete this ${drawingType} drawing?`);
      if (confirmed) {
        // Store current zoom level before deletion to preserve it
        const currentZoom = chartRef.current?.getOption()?.dataZoom?.[0];
        const currentStart = currentZoom?.start || 0;
        const currentEnd = currentZoom?.end || 100;

        console.log(`🗑️ Deleting ${drawingType} drawing, preserving zoom: ${currentStart}% - ${currentEnd}%`);

        setDrawings(prev => prev.filter((_, i) => i !== index));

        // Restore zoom level after a brief delay to ensure chart has updated
        setTimeout(() => {
          if (chartRef.current) {
            chartRef.current.dispatchAction({
              type: 'dataZoom',
              start: currentStart,
              end: currentEnd
            });
            console.log(`✅ Zoom level restored after deletion: ${currentStart}% - ${currentEnd}%`);
          }
        }, 100);
      }
    }
  };

  // Handle chart click for drawing
  const handleChartClick = (params: any, echarts?: any) => {
    console.log('🖱️ Chart click detected:', {
      activeTool,
      isDrawingMode,
      hasChartRef: !!chartRef.current,
      params: params
    });

    // Handle delete mode - simplified approach
    if (activeTool === 'delete') {

      // Check if clicked on a delete button
      if (params.seriesName && params.seriesName.startsWith('DeleteButton_')) {
        const parts = params.seriesName.split('_');
        const drawingIndex = parseInt(parts[1]);

        if (!isNaN(drawingIndex)) {
          deleteDrawing(drawingIndex);
          setActiveTool(null); // Exit delete mode after deletion
        }
        return;
      }

      // Check if clicked directly on a drawing series
      if (params.seriesName && params.seriesName.startsWith('Drawing_')) {
        const parts = params.seriesName.split('_');
        const drawingIndex = parseInt(parts[1]);

        if (!isNaN(drawingIndex)) {
          deleteDrawing(drawingIndex);
          setActiveTool(null); // Exit delete mode after deletion
        }
      }
      return;
    }

    if (!isDrawingMode || !activeTool || !chartRef.current) {
      console.log('❌ Drawing conditions not met:', {
        isDrawingMode,
        activeTool,
        hasChartRef: !!chartRef.current
      });
      return;
    }

    console.log('✅ Drawing conditions met, proceeding with drawing action');

    try {
      let point;

      if (params.event && params.event.offsetX && params.event.offsetY) {
        // Use ECharts coordinate conversion for accurate positioning
        const pointInPixel = [params.event.offsetX, params.event.offsetY];
        const pointInGrid = chartRef.current.convertFromPixel('grid', pointInPixel);

        if (pointInGrid && pointInGrid.length >= 2) {
          const [timeIndex, price] = pointInGrid;

          const timeValue = stockData[Math.floor(timeIndex)]?.time || stockData[0]?.time;
          const actualTime = typeof timeValue === 'number' ? new Date(timeValue * 1000).toISOString() : timeValue;

          point = {
            time: actualTime,
            price: price
          };
        }
      } else if (params.dataIndex !== undefined && params.value) {
        // Direct click on data point
        const timeValue = stockData[params.dataIndex]?.time || stockData[0]?.time;
        const actualTime = typeof timeValue === 'number' ? new Date(timeValue * 1000).toISOString() : timeValue;

        point = {
          time: actualTime,
          price: params.value[2] // Close price
        };
      }

      if (point) {
        console.log('Drawing point:', point, 'Tool:', activeTool);
        handleDrawingAction(point);
      } else {
        console.log('Could not determine click coordinates');
      }
    } catch (error) {
      console.error('Error in chart click:', error);
    }
  };

  // Handle mouse move for drawing preview - improved cursor tracking with tooltip protection
  const handleChartMouseMove = (params: any, echarts?: any) => {
    // Only process when actively drawing to prevent tooltip interference
    if (!isDrawingMode || !activeTool || !chartRef.current || !isDrawing || !currentDrawing) return;

    try {
      let point = null;

      // Use ECharts coordinate conversion for mouse move with better error handling
      if (params.event && params.event.offsetX !== undefined && params.event.offsetY !== undefined) {
        const pointInPixel = [params.event.offsetX, params.event.offsetY];

        // Safely attempt coordinate conversion
        try {
          const pointInGrid = chartRef.current.convertFromPixel('grid', pointInPixel);

          if (pointInGrid && Array.isArray(pointInGrid) && pointInGrid.length >= 2 &&
              typeof pointInGrid[0] === 'number' && typeof pointInGrid[1] === 'number') {
            const [timeIndex, price] = pointInGrid;

            // Ensure we have valid data bounds
            if (stockData && stockData.length > 0) {
              const safeIndex = Math.floor(Math.max(0, Math.min(timeIndex, stockData.length - 1)));
              const timeValue = stockData[safeIndex]?.time || stockData[0]?.time;
              const actualTime = typeof timeValue === 'number' ? new Date(timeValue * 1000).toISOString() : timeValue;

              point = {
                time: actualTime,
                price: price
              };
            }
          }
        } catch (conversionError) {
          // Silently handle coordinate conversion errors to prevent tooltip glitching
          return;
        }
      }

      // Update current drawing preview when we have a valid point
      if (point && currentDrawing && currentDrawing.points && currentDrawing.points.length > 0) {
        switch (activeTool) {
          case 'line':
            setCurrentDrawing(prev => {
              if (!prev || !prev.points || !prev.points[0]) return prev;
              return {
                ...prev,
                points: [prev.points[0], point]
              };
            });
            break;

          case 'circle':
            setCurrentDrawing(prev => {
              if (!prev || !prev.points || !prev.points[0]) return prev;
              return {
                ...prev,
                points: [prev.points[0], point]
              };
            });
            break;

          case 'rectangle':
            setCurrentDrawing(prev => {
              if (!prev || !prev.points || !prev.points[0]) return prev;
              return {
                ...prev,
                points: [prev.points[0], point]
              };
            });
            break;

          case 'measure':
            setCurrentDrawing(prev => {
              if (!prev || !prev.points || !prev.points[0]) return prev;
              return {
                ...prev,
                points: [prev.points[0], point]
              };
            });
            break;
        }
      }
    } catch (error) {
      // Silently handle errors to prevent tooltip interference
      console.debug('Drawing mouse move error:', error);
    }
  };

  // Function to fetch stock data
  const fetchData = useCallback(async (timeframe: string) => {
    setIsLoading(true);
    setError(null);

    try {
      let multiplier = 1;
      let timespanUnit = 'hour';
      let days = 7;

      // Set the appropriate timeframe parameters for Polygon API
      // Adjusted ranges based on Polygon API limitations and 5-year zoom capability
      switch (timeframe) {
        case '1m':
          multiplier = 1;
          timespanUnit = 'minute';
          days = 7; // 1-minute data limited to 7 days maximum
          break;
        case '5m':
          multiplier = 5;
          timespanUnit = 'minute';
          days = 7; // 5-minute data limited to 7 days maximum
          break;
        case '15m':
          multiplier = 15;
          timespanUnit = 'minute';
          days = 7; // 15-minute data limited to 7 days maximum
          break;
        case '1h':
          multiplier = 1;
          timespanUnit = 'hour';
          days = 365 * 5; // 5 years of 1-hour candles
          break;
        case '4h':
          multiplier = 4;
          timespanUnit = 'hour';
          days = 365 * 5; // 5 years of 4-hour candles
          break;
        case '1d':
          multiplier = 1;
          timespanUnit = 'day';
          days = 365 * 5; // 5 years of daily candles
          break;
        case '1M':
          multiplier = 1;
          timespanUnit = 'month';
          days = 365 * 5; // 5 years of monthly candles
          break;
        case '1Y':
          multiplier = 1;
          timespanUnit = 'year';
          days = 365 * 20; // 20 years of yearly candles (keep existing for yearly)
          break;
        default:
          multiplier = 1;
          timespanUnit = 'hour';
          days = 365 * 5; // Default to 5 years
      }

      try {
        const dateRange = getDateRangeForLastDays(days);

        // Debug logging for date range
        if (activeTimeframe === '1h' || activeTimeframe === '4h') {
          console.log(`📅 Date range debug for ${activeTimeframe}:`, {
            timeframe: activeTimeframe,
            days,
            dateRange,
            currentDate: new Date().toISOString(),
            multiplier,
            timespanUnit
          });
        }

        let stockData = await fetchStockData(
          currentSymbol,
          multiplier,
          timespanUnit,
          dateRange.from,
          dateRange.to
        );

        // If we didn't get enough data for zoom capability, try a longer range
        if (stockData.length < 100 && days < 365 * 5) {
          console.log(`📊 Got only ${stockData.length} data points, trying longer range...`);
          // For minute timeframes (1m, 5m, 15m), max out at 7 days; for others, try 5x the original range
          const maxDays = ['1m', '5m', '15m'].includes(activeTimeframe) ? 7 : 365 * 5;
          const extendedDays = Math.min(days * 5, maxDays);
          const extendedDateRange = getDateRangeForLastDays(extendedDays);

          try {
            const extendedStockData = await fetchStockData(
              currentSymbol,
              multiplier,
              timespanUnit,
              extendedDateRange.from,
              extendedDateRange.to
            );

            if (extendedStockData.length > stockData.length) {
              console.log(`📊 Extended range returned ${extendedStockData.length} data points (improvement!)`);
              stockData = extendedStockData;
            }
          } catch (extendedError) {
            console.log('📊 Extended range failed, using original data:', extendedError);
          }
        }

        const chartData = convertToChartData(stockData);

        // Debug logging for data length and time span
        console.log(`📊 Data fetched for ${activeTimeframe}:`, {
          timeframe: activeTimeframe,
          dataPoints: chartData.length,
          requestedDays: days,
          firstDataPoint: chartData.length > 0 ? new Date(chartData[0].time * 1000).toISOString() : 'No data',
          lastDataPoint: chartData.length > 0 ? new Date(chartData[chartData.length - 1].time * 1000).toISOString() : 'No data',
          timeSpanDays: chartData.length > 1 ? Math.round((chartData[chartData.length - 1].time - chartData[0].time) / (24 * 60 * 60)) : 0,
          ...(['1m', '5m', '15m'].includes(activeTimeframe) ? { note: `${activeTimeframe} data is limited to 7 days maximum for performance` } : {})
        });

        // Debug logging for timestamp issues
        if (chartData.length > 0 && (activeTimeframe === '1h' || activeTimeframe === '4h')) {
          console.log(`🔍 Debug ${activeTimeframe} data:`, {
            timeframe: activeTimeframe,
            rawDataSample: stockData.slice(0, 3).map(d => ({
              rawTimestamp: d.t,
              rawDate: new Date(d.t),
              rawDateISO: new Date(d.t).toISOString()
            })),
            convertedDataSample: chartData.slice(0, 3).map(d => ({
              convertedTime: d.time,
              convertedDate: new Date(d.time * 1000),
              convertedDateISO: new Date(d.time * 1000).toISOString()
            }))
          });
        }

        setStockData(chartData);

        // Update price info with the latest data
        if (chartData.length > 0) {
          const latestData = chartData[chartData.length - 1];
          const newPriceInfo = {
            open: latestData.open,
            high: latestData.high,
            low: latestData.low,
            close: latestData.close,
            volume: latestData.volume || 0
          };
          setPriceInfo(newPriceInfo);

          // Store as last valid data since fetch was successful
          setLastValidSymbol(currentSymbol);
          setLastValidData(chartData);
          setLastValidPriceInfo(newPriceInfo);
        }
      } catch (apiError: any) {
        console.error('API Error:', apiError);
        throw apiError; // Re-throw to be caught by the outer catch
      }
    } catch (err) {
      console.error('Error fetching stock data:', err);

      // Check if we have last valid data to preserve
      if (lastValidData.length > 0 && lastValidSymbol !== currentSymbol) {
        // Keep the last valid chart displayed silently without error message
        setError(null); // Clear any error message
        setStockData(lastValidData);
        setPriceInfo(lastValidPriceInfo);
        console.log(`Silently preserving last valid chart data for ${lastValidSymbol} while user entered invalid ticker ${currentSymbol}`);
      } else {
        // No valid data to preserve, use fallback data
        setError('Failed to fetch stock data. Using simulated data instead.');

        // Generate fallback data that matches the selected timeframe
        let fallbackData: CandlestickData[];
        fallbackData = generateCandleDataForTimeframe(timeframe);

        setStockData(fallbackData);

        // Update price info with the latest fallback data
        if (fallbackData.length > 0) {
          const latestData = fallbackData[fallbackData.length - 1];
          setPriceInfo({
            open: latestData.open,
            high: latestData.high,
            low: latestData.low,
            close: latestData.close,
            volume: 1000000 // Default volume for fallback data
          });
        }
      }
    } finally {
      setIsLoading(false);
    }
  }, [currentSymbol]);



  // Initialize chart and fetch data
  useEffect(() => {
    // Debug current date for reference
    const now = new Date();
    console.log(`🕐 Current date reference:`, {
      currentDate: now.toISOString(),
      currentMonth: now.getMonth() + 1,
      currentDay: now.getDate(),
      currentYear: now.getFullYear(),
      timeframe: activeTimeframe,
      timestamp: now.getTime(),
      userSaysCurrentDate: 'June 18, 2025',
      note: 'If this shows wrong date, there may be a system clock issue'
    });

    // Initial data load
    fetchData(activeTimeframe);
  }, [activeTimeframe, fetchData]);

  // Load drawings when symbol changes (ONLY when symbol changes, not chart bounds)
  useEffect(() => {
    if (currentSymbol) {
      console.log(`📂 Loading drawings for symbol: ${currentSymbol}`);
      const savedDrawings = loadDrawingsForSymbol(currentSymbol);
      console.log(`📂 Loaded ${savedDrawings.length} drawings for ${currentSymbol}`);

      // For now, just load the drawings as-is to prevent the clearing issue
      setDrawings(savedDrawings);
    }
  }, [currentSymbol]); // REMOVED chartBounds dependency to prevent reloading after drawing

  // Auto-save drawings when they change (with debouncing)
  useEffect(() => {
    if (currentSymbol && drawings.length >= 0) {
      // Use auto-save with debouncing to prevent excessive localStorage writes
      autoSaveDrawings(currentSymbol, drawings, 1000);
    }
  }, [currentSymbol, drawings]);

  // Update chart bounds when stock data changes and adjust drawings for new timeframe
  useEffect(() => {
    // Skip bounds updates when chart is locked for drawing to prevent chart shifts
    if (drawingLocked || isDrawing) {
      console.log('❌ Chart bounds update skipped: chart locked for drawing');
      return;
    }

    if (stockData && stockData.length > 0) {
      const newBounds = calculateChartBounds(stockData);

      // If we have existing bounds and drawings, update drawings for new timeframe
      if (chartBounds && drawings.length > 0) {
        const updatedDrawings = drawings.map(drawing => {
          // If drawing has relative coordinates, convert to absolute with new bounds
          if (drawing.relativePoints || drawing.relativeCenter) {
            return convertDrawingToAbsolute(drawing, newBounds);
          }

          // If drawing doesn't have relative coordinates, convert it to have both
          if (drawing.points || drawing.center) {
            return storeDrawingWithBothCoordinates(drawing, newBounds);
          }

          return drawing;
        });

        setDrawings(updatedDrawings);
        console.log(`🔄 Updated ${updatedDrawings.length} drawings for new timeframe/bounds`);
      }

      setChartBounds(newBounds);
    }
  }, [stockData, activeTimeframe, isDrawing, drawingLocked]);

  // Keyboard shortcuts for drawing tools
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Only handle shortcuts when not typing in input fields
      if (event.target instanceof HTMLInputElement) return;

      switch (event.key.toLowerCase()) {
        case 'escape':
          resetDrawingStates('escape key pressed', true); // Preserve zoom when using ESC
          break;
        case 'l':
          if (!event.ctrlKey && !event.metaKey) {
            handleToolSelect('line');
          }
          break;
        case 'c':
          if (!event.ctrlKey && !event.metaKey) {
            handleToolSelect('circle');
          }
          break;
        case 'r':
          if (!event.ctrlKey && !event.metaKey) {
            handleToolSelect('rectangle');
          }
          break;
        case 'm':
          if (!event.ctrlKey && !event.metaKey) {
            handleToolSelect('measure');
          }
          break;

        case 'd':
          if (!event.ctrlKey && !event.metaKey) {
            handleToolSelect('delete');
          }
          break;
        case 'delete':
        case 'backspace':
          if (event.ctrlKey || event.metaKey) {
            handleToolSelect('clear');
          }
          break;
        case 'q':
          // Emergency reset - force unlock chart if it gets stuck
          if (event.ctrlKey || event.metaKey) {
            console.log('🚨 Emergency chart reset triggered (Ctrl/Cmd+Q)');
            resetDrawingStates('emergency reset', true); // Preserve zoom during emergency reset

            // Additional recovery steps for cursor freezing
            setTimeout(() => {
              if (chartRef.current) {
                try {
                  // Force chart to be responsive again
                  chartRef.current.resize();
                  const newOption = getChartOptions();
                  chartRef.current.setOption(newOption, {
                    notMerge: false,
                    lazyUpdate: false,
                    silent: true
                  });
                  console.log('✅ Emergency chart recovery completed');
                } catch (error) {
                  console.error('Emergency recovery failed:', error);
                }
              }
            }, 200);

            event.preventDefault();
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [showDrawingTools, resetDrawingStates]);

  // Note: Removed problematic chart re-render effect that was causing candlestick animation issues
  // The drawing preview is now handled directly in the chart options without forcing re-renders

  // Chart type change handling - minimal intervention
  useEffect(() => {
    // Don't do anything if we're drawing
    if (isDrawing || currentDrawing || drawingLocked) {
      console.log('❌ Chart type change handling skipped: drawing in progress');
      return;
    }

    // Just ensure chart is responsive - no resets or other nonsense
    if (chartRef.current) {
      setTimeout(() => {
        if (chartRef.current && !isDrawing && !currentDrawing && !drawingLocked) {
          try {
            chartRef.current.resize();
            console.log('✅ Chart type change responsiveness check completed');
          } catch (error) {
            console.error('Chart type change responsiveness check failed:', error);
          }
        }
      }, 200);
    }
  }, [chartSettings.chartType]);

  // Removed chart options update effect - it was causing unnecessary updates when drawing tools are selected

  // Robust auto-scaling function to adjust y-axis based on visible data
  const autoScaleYAxis = useCallback((startPercent: number = 0, endPercent: number = 100) => {
    // Skip auto-scaling when chart is locked for drawing, recently unlocked, OR when any drawing tool is active
    if (drawingLocked || isDrawing || recentlyUnlocked || activeTool || isDrawingMode || !chartRef.current || !stockData.length) {
      if (drawingLocked || isDrawing || recentlyUnlocked || activeTool || isDrawingMode) {
        console.log('❌ Auto-scaling skipped: drawing tool active or chart locked');
      } else {
        console.log('❌ Auto-scaling skipped: no chart ref or data');
      }
      return;
    }

    // Calculate precise data indices
    const totalDataPoints = stockData.length;
    const startIndex = Math.max(0, Math.floor((startPercent / 100) * totalDataPoints));
    const endIndex = Math.min(totalDataPoints, Math.ceil((endPercent / 100) * totalDataPoints));

    // Ensure we have at least one data point
    const actualEndIndex = Math.max(startIndex + 1, endIndex);
    const visibleData = stockData.slice(startIndex, actualEndIndex);

    if (visibleData.length === 0) {
      console.log('❌ Auto-scaling skipped: no visible data');
      return;
    }

    // Get all price points from visible candles (high and low)
    const allPrices = [];
    for (const candle of visibleData) {
      allPrices.push(candle.high, candle.low);
      // Also include open and close for better range calculation
      allPrices.push(candle.open, candle.close);
    }

    const minPrice = Math.min(...allPrices);
    const maxPrice = Math.max(...allPrices);
    const priceRange = maxPrice - minPrice;

    // Dynamic padding based on zoom level and price range
    let paddingPercent = 0.15; // Default 15% padding
    const zoomSpan = endPercent - startPercent;

    // More padding when zoomed out to see more context
    if (zoomSpan > 80) paddingPercent = 0.2;  // 20% when showing most data
    else if (zoomSpan > 50) paddingPercent = 0.15; // 15% when moderately zoomed
    else if (zoomSpan > 20) paddingPercent = 0.1;  // 10% when zoomed in
    else paddingPercent = 0.05; // 5% when very zoomed in

    const padding = Math.max(priceRange * paddingPercent, Math.abs(minPrice) * 0.01, 0.01);
    const adjustedMin = minPrice - padding;
    const adjustedMax = maxPrice + padding;



    // Force update Y-axis with complete configuration - INSTANT UPDATE
    try {
      chartRef.current.setOption({
        yAxis: [{
          min: adjustedMin,
          max: adjustedMax,
          scale: true,
          type: chartSettings.priceScaleMode === 'logarithmic' ? 'log' : 'value',
          splitNumber: 6,
          position: 'right',
          axisLine: { lineStyle: { color: chartSettings.gridColor } },
          axisLabel: {
            color: chartSettings.textColor,
            fontFamily: "'Roboto Mono', monospace",
            fontSize: 10,
            inside: false,
            formatter: (value: number) => {
              if (value >= 1000) return value.toFixed(0);
              else if (value >= 100) return value.toFixed(1);
              else if (value >= 10) return value.toFixed(2);
              else return value.toFixed(3);
            }
          },
          axisTick: { show: false },
          splitLine: {
            show: chartSettings.showGrid,
            lineStyle: { color: chartSettings.gridColor, type: 'solid' }
          },
          // Disable animations for instant response
          animation: false,
          animationDuration: 0
        }]
      }, {
        notMerge: false, // Merge to preserve other chart settings
        lazyUpdate: false, // Apply immediately
        silent: false // Allow immediate visual update
      });

    } catch (error) {
      // Silently handle auto-scaling errors
    }
  }, [stockData, chartSettings, drawingLocked, recentlyUnlocked]);

  // Ensure auto-scaling is maintained - periodic check
  useEffect(() => {
    if (!chartRef.current || !stockData.length) return;

    const intervalId = setInterval(() => {
      // Skip periodic auto-scaling when chart is locked for drawing or recently unlocked
      if (drawingLocked || isDrawing || recentlyUnlocked || !chartRef.current || !stockData.length) return;

      if (chartRef.current && stockData.length > 0) {
        const currentZoom = chartRef.current.getOption()?.dataZoom?.[0];
        if (currentZoom && (currentZoom.start !== undefined && currentZoom.end !== undefined)) {
          // Only re-apply if the zoom range is reasonable (not during rapid changes)
          const zoomSpan = currentZoom.end - currentZoom.start;
          if (zoomSpan > 0.1 && zoomSpan <= 100) {
            // Call auto-scaling directly without dependency
            const startPercent = currentZoom.start;
            const endPercent = currentZoom.end;

            // Calculate precise data indices
            const totalDataPoints = stockData.length;
            const startIndex = Math.max(0, Math.floor((startPercent / 100) * totalDataPoints));
            const endIndex = Math.min(totalDataPoints, Math.ceil((endPercent / 100) * totalDataPoints));
            const actualEndIndex = Math.max(startIndex + 1, endIndex);
            const visibleData = stockData.slice(startIndex, actualEndIndex);

            if (visibleData.length > 0) {
              const allPrices = [];
              for (const candle of visibleData) {
                allPrices.push(candle.high, candle.low, candle.open, candle.close);
              }

              const minPrice = Math.min(...allPrices);
              const maxPrice = Math.max(...allPrices);
              const priceRange = maxPrice - minPrice;
              const zoomSpan = endPercent - startPercent;

              let paddingPercent = 0.15;
              if (zoomSpan > 80) paddingPercent = 0.2;
              else if (zoomSpan > 50) paddingPercent = 0.15;
              else if (zoomSpan > 20) paddingPercent = 0.1;
              else paddingPercent = 0.05;

              const padding = Math.max(priceRange * paddingPercent, Math.abs(minPrice) * 0.01, 0.01);
              const adjustedMin = minPrice - padding;
              const adjustedMax = maxPrice + padding;

              chartRef.current.setOption({
                yAxis: [{
                  min: adjustedMin,
                  max: adjustedMax,
                  scale: true,
                  animation: false,
                  animationDuration: 0
                }]
              }, { notMerge: false, lazyUpdate: false, silent: true });
            }
          }
        }
      }
    }, 500); // Check every 500ms for more responsive updates

    return () => clearInterval(intervalId);
  }, [stockData, isDrawing, drawingLocked, recentlyUnlocked]);

  // Reset chart zoom and auto-scale when timeframe changes (NOT when drawing states change)
  useEffect(() => {
    // Skip zoom/scale reset when chart is locked for drawing to prevent chart shifts
    if (drawingLocked || isDrawing) {
      console.log('❌ Chart zoom/scale reset skipped: chart locked for drawing');
      return;
    }

    // Skip zoom reset if we're in the middle of preserving zoom during state reset
    if (preventZoomReset) {
      console.log('❌ Chart zoom/scale reset skipped: zoom preservation in progress');
      return;
    }

    if (chartRef.current && stockData.length > 0) {
      // SKIP automatic zoom when drawing tools are active to prevent unwanted zoom changes
      if (isDrawingMode || activeTool || isDrawing || drawingLocked) {
        console.log('⏸️ Skipping automatic zoom - drawing tools are active');
        return;
      }

      // Immediate execution for instant response
      const applyZoomAndScale = () => {
        if (chartRef.current && !isDrawingMode && !activeTool && !isDrawing && !drawingLocked) {
          // Start zoomed in based on timeframe, allow users to zoom out
          let initialStart = 85; // Default to showing last 15% of data

          // Adjust initial zoom based on timeframe
          switch (activeTimeframe) {
            case '1m':
            case '5m':
            case '15m':
              initialStart = 90; // Show last 10% for minute timeframes (more zoomed in)
              break;
            case '1h':
              initialStart = 85; // Show last 15% for hourly
              break;
            case '4h':
            case '1d':
            case '1month':
            case '1year':
              initialStart = 80; // Show last 20% for longer timeframes
              break;
          }

          chartRef.current.dispatchAction({
            type: 'dataZoom',
            start: initialStart, // Start zoomed in, users can zoom out to see full range
            end: 100   // Always end at current time
          });

          // INSTANT auto-scale y-axis for initial view
          autoScaleYAxis(initialStart, 100);
        }
      };

      // Apply immediately
      applyZoomAndScale();

      // Also apply after a tiny delay to ensure chart is ready
      setTimeout(applyZoomAndScale, 10);
    }
  }, [activeTimeframe, stockData, autoScaleYAxis, preventZoomReset]); // Only reset zoom when timeframe or data changes, respect zoom prevention flag

  // Improved click handler for chart container with better coordinate conversion
  const handleContainerClick = (event: React.MouseEvent<HTMLDivElement>) => {
    console.log('🖱️ Container click detected:', {
      activeTool,
      isDrawingMode,
      hasChartRef: !!chartRef.current
    });

    // In delete mode, don't handle container clicks - let ECharts handle them
    if (activeTool === 'delete') {
      console.log('Delete mode - letting ECharts handle click');
      return;
    }

    if (!isDrawingMode || !activeTool || !chartRef.current) {
      console.log('❌ Container click conditions not met:', {
        isDrawingMode,
        activeTool,
        hasChartRef: !!chartRef.current
      });
      return;
    }

    console.log('✅ Container click conditions met, proceeding with drawing');

    // Prevent event bubbling
    event.stopPropagation();

    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    try {
      // Use ECharts coordinate conversion for accurate positioning
      const pointInPixel = [x, y];
      const pointInGrid = chartRef.current.convertFromPixel('grid', pointInPixel);

      if (pointInGrid && pointInGrid.length >= 2) {
        const [timeIndex, price] = pointInGrid;

        // Ensure timeIndex is within bounds
        const clampedTimeIndex = Math.max(0, Math.min(Math.floor(timeIndex), stockData.length - 1));
        const timeValue = stockData[clampedTimeIndex]?.time || stockData[0]?.time;
        const actualTime = typeof timeValue === 'number' ? new Date(timeValue * 1000).toISOString() : timeValue;

        const point = {
          time: actualTime,
          price: price
        };

        handleDrawingAction(point);
      } else {
        // Improved fallback calculation
        const chartContainer = event.currentTarget.querySelector('.echarts-for-react');
        if (chartContainer) {
          const chartRect = chartContainer.getBoundingClientRect();
          const relativeX = (x - (rect.left - chartRect.left)) / chartRect.width;
          const relativeY = (y - (rect.top - chartRect.top)) / chartRect.height;

          // Account for chart margins (approximately 8% left, 5% right, 8% top, 17% bottom)
          const adjustedX = Math.max(0, Math.min(1, (relativeX - 0.08) / (1 - 0.08 - 0.05)));
          const adjustedY = Math.max(0, Math.min(1, (relativeY - 0.08) / (1 - 0.08 - 0.17)));

          const dataIndex = Math.floor(adjustedX * stockData.length);
          const prices = stockData.map(d => [d.low, d.high]).flat();
          const minPrice = Math.min(...prices);
          const maxPrice = Math.max(...prices);
          const price = maxPrice - (adjustedY * (maxPrice - minPrice));

          const clampedDataIndex = Math.max(0, Math.min(dataIndex, stockData.length - 1));
          const timeValue = stockData[clampedDataIndex]?.time || stockData[0]?.time;
          const actualTime = typeof timeValue === 'number' ? new Date(timeValue * 1000).toISOString() : timeValue;

          const point = {
            time: actualTime,
            price: Math.max(minPrice, Math.min(price, maxPrice))
          };

          handleDrawingAction(point);
        }
      }
    } catch (error) {
      // Silently handle coordinate conversion errors
    }
  };

  // Extracted drawing action logic
  const handleDrawingAction = (point: { time: string; price: number }) => {
    console.log('🎨 Drawing action triggered:', {
      activeTool,
      point,
      isDrawing,
      currentDrawing
    });

    switch (activeTool) {
      case 'line':
        if (!isDrawing) {
          // Start drawing - LOCK chart immediately to prevent any updates
          console.log('🔒 LOCKING chart for drawing');
          setDrawingLocked(true);
          setCurrentDrawing({
            type: 'line',
            points: [point]
          });
          setIsDrawing(true);
        } else {
          // Finish drawing
          const newDrawing = {
            type: 'line',
            points: [currentDrawing.points[0], point],
            id: Date.now()
          };

          // Store with both absolute and relative coordinates if bounds are available
          const drawingToStore = chartBounds
            ? storeDrawingWithBothCoordinates(newDrawing, chartBounds)
            : newDrawing;

          setDrawings(prev => [...prev, drawingToStore]);
          setCurrentDrawing(null);
          setIsDrawing(false);
          // UNLOCK chart after drawing is complete with delay to prevent immediate auto-scaling
          console.log('🔓 UNLOCKING chart after drawing');
          setDrawingLocked(false);
          setRecentlyUnlocked(true);
          // Clear the recently unlocked flag after a brief delay
          setTimeout(() => {
            setRecentlyUnlocked(false);
            console.log('✅ Chart fully unlocked - auto-scaling can resume');
          }, 500); // Reduced to 500ms for faster recovery
        }
        break;

      case 'circle':
        console.log('🔵 Circle drawing action triggered:', { isDrawing, point });
        if (!isDrawing) {
          // Start drawing circle - LOCK chart immediately
          console.log('🔒 LOCKING chart for circle drawing');
          setDrawingLocked(true);
          setCurrentDrawing({
            type: 'circle',
            points: [point]
          });
          setIsDrawing(true);
          console.log('🔵 Circle drawing started with center point:', point);
        } else {
          // Finish drawing circle - second point determines radius
          console.log('🔵 Finishing circle drawing with radius point:', point);
          const newDrawing = {
            type: 'circle',
            points: [currentDrawing.points[0], point],
            id: Date.now()
          };

          // Store the drawing directly for now to debug the issue
          console.log('🔵 Storing circle drawing:', newDrawing);
          setDrawings(prev => {
            const updated = [...prev, newDrawing];
            console.log('🔵 Updated drawings array:', updated);
            return updated;
          });
          setCurrentDrawing(null);
          setIsDrawing(false);
          // UNLOCK chart after drawing is complete
          console.log('🔓 UNLOCKING chart after circle drawing');
          setDrawingLocked(false);
          setRecentlyUnlocked(true);
          setTimeout(() => {
            setRecentlyUnlocked(false);
            console.log('✅ Chart fully unlocked - auto-scaling can resume');
          }, 500); // Reduced to 500ms for faster recovery
          console.log('🔵 Circle drawing completed and added to drawings');
        }
        break;

      case 'rectangle':
        console.log('🔲 Rectangle drawing action triggered:', { isDrawing, point });
        if (!isDrawing) {
          // Start drawing rectangle - LOCK chart immediately
          console.log('🔒 LOCKING chart for rectangle drawing');
          setDrawingLocked(true);
          setCurrentDrawing({
            type: 'rectangle',
            points: [point]
          });
          setIsDrawing(true);
          console.log('🔲 Rectangle drawing started with first corner:', point);
        } else {
          // Finish drawing rectangle
          console.log('🔲 Finishing rectangle drawing with second corner:', point);
          const newDrawing = {
            type: 'rectangle',
            points: [currentDrawing.points[0], point],
            id: Date.now()
          };

          // Store the drawing directly for now to debug the issue
          console.log('🔲 Storing rectangle drawing:', newDrawing);
          setDrawings(prev => {
            const updated = [...prev, newDrawing];
            console.log('🔲 Updated drawings array:', updated);
            return updated;
          });
          setCurrentDrawing(null);
          setIsDrawing(false);
          // UNLOCK chart after drawing is complete
          console.log('🔓 UNLOCKING chart after rectangle drawing');
          setDrawingLocked(false);
          setRecentlyUnlocked(true);
          setTimeout(() => {
            setRecentlyUnlocked(false);
            console.log('✅ Chart fully unlocked - auto-scaling can resume');
          }, 500); // Reduced to 500ms for faster recovery
          console.log('🔲 Rectangle drawing completed and added to drawings');
        }
        break;

      case 'measure':
        if (!isDrawing) {
          // Start measuring
          setCurrentDrawing({
            type: 'measure',
            points: [point]
          });
          setIsDrawing(true);
        } else {
          // Finish measuring
          const newDrawing = {
            type: 'measure',
            points: [currentDrawing.points[0], point],
            id: Date.now()
          };

          // Store with both absolute and relative coordinates if bounds are available
          const drawingToStore = chartBounds
            ? storeDrawingWithBothCoordinates(newDrawing, chartBounds)
            : newDrawing;

          setDrawings(prev => [...prev, drawingToStore]);
          setCurrentDrawing(null);
          setIsDrawing(false);
        }
        break;





      default:
        break;
    }
  };

  // Function to create drawing series from drawings array - improved for zoom persistence
  const createDrawingSeries = () => {
    const drawingSeries: any[] = [];

    console.log('🎨 Creating drawing series from drawings:', drawings);

    drawings.forEach((drawing, index) => {
      console.log(`🎨 Processing drawing ${index}:`, drawing);

      // Add delete button for each drawing when in delete mode
      if (activeTool === 'delete' && drawing.points && drawing.points.length > 0) {
        // Get the first point of the drawing to position the delete button
        const firstPoint = drawing.points[0];
        drawingSeries.push({
          name: `DeleteButton_${index}`,
          type: 'scatter',
          coordinateSystem: 'cartesian2d',
          data: [[firstPoint.time, firstPoint.price]],
          symbol: 'circle',
          symbolSize: 20,
          itemStyle: {
            color: '#ff4444',
            borderColor: '#ffffff',
            borderWidth: 2
          },
          label: {
            show: true,
            formatter: '✕',
            color: '#ffffff',
            fontSize: 12,
            fontWeight: 'bold'
          },
          animation: false,
          silent: false,
          z: 2000,
          zlevel: 2
        });
      }

      switch (drawing.type) {
        case 'line':
          // Show exactly what the user drew - no extension
          // Ensure coordinates are properly formatted for persistence across zoom
          const lineData = drawing.points.map((point: any) => {
            // Always use ISO string format for time consistency
            let timeValue: string;
            if (typeof point.time === 'string') {
              timeValue = point.time;
            } else if (typeof point.time === 'number') {
              timeValue = new Date(point.time).toISOString();
            } else {
              timeValue = new Date(point.time).toISOString();
            }
            return [timeValue, point.price];
          });

          drawingSeries.push({
            name: `Drawing_${index}`,
            type: 'line',
            coordinateSystem: 'cartesian2d',
            xAxisIndex: 0,
            yAxisIndex: 0,
            data: lineData,
            lineStyle: {
              color: chartSettings.defaultLineColor,
              width: chartSettings.defaultLineWidth,
              type: 'solid'
            },
            symbol: 'none',
            symbolSize: 0,
            animation: false,
            silent: false, // Allow clicking for delete functionality
            z: 1000,
            zlevel: 1,
            clip: false, // Prevent clipping
            large: false, // Disable large mode
            progressive: 0, // Disable progressive rendering
            emphasis: {
              disabled: true // Disable emphasis to prevent red highlighting
            },

          });

          // In delete mode, make the line more prominent for easier targeting
          if (activeTool === 'delete') {
            // Add a thicker invisible line for easier clicking
            drawingSeries.push({
              name: `Drawing_${index}_hitarea`,
              type: 'line',
              coordinateSystem: 'cartesian2d',
              xAxisIndex: 0,
              yAxisIndex: 0,
              data: lineData,
              lineStyle: {
                color: 'transparent',
                width: 15 // Wide invisible line for easier clicking
              },
              symbol: 'none',
              animation: false,
              silent: false,
              z: 999, // Just below the visible line
              zlevel: 1,
              clip: false
            });
          }
          break;

        case 'circle':
          // Create circle using center and radius point
          console.log('🔵 Rendering circle drawing:', drawing);
          if (drawing.points && drawing.points.length >= 2) {
            const [center, radiusPoint] = drawing.points;
            console.log('🔵 Circle points:', { center, radiusPoint });

            const centerTime = new Date(center.time).getTime();
            const radiusTime = new Date(radiusPoint.time).getTime();
            const timeDiff = Math.abs(radiusTime - centerTime);
            const priceDiff = Math.abs(radiusPoint.price - center.price);

            console.log('🔵 Circle calculations:', { centerTime, radiusTime, timeDiff, priceDiff });

            // Create circle points (simplified as octagon for performance)
            const circlePoints = [];
            for (let i = 0; i <= 16; i++) {
              const angle = (i / 16) * 2 * Math.PI;
              const timeOffset = Math.cos(angle) * timeDiff;
              const priceOffset = Math.sin(angle) * priceDiff;
              const pointTime = new Date(centerTime + timeOffset).toISOString();
              const pointPrice = center.price + priceOffset;
              circlePoints.push([pointTime, pointPrice]);
            }

            console.log('🔵 Circle points generated:', circlePoints.length, 'points');

            const circleSeries = {
              name: `Drawing_${index}`,
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: circlePoints,
              lineStyle: {
                color: '#00e7b6',
                width: 2,
                type: 'solid'
              },
              symbol: 'none',
              animation: false,
              silent: false, // Allow clicking for delete functionality
              z: 1000,
              zlevel: 1,
              clip: false
            };

            console.log('🔵 Adding circle series to chart:', circleSeries);
            drawingSeries.push(circleSeries);
          } else {
            console.log('🔵 Circle drawing skipped - insufficient points:', drawing.points);
          }
          break;

        case 'rectangle':
          console.log('🔲 Rendering rectangle drawing:', drawing);
          if (drawing.points && drawing.points.length >= 2) {
            const [start, end] = drawing.points;
            console.log('🔲 Rectangle points:', { start, end });

            const rectangleData = [
              [start.time, start.price],
              [end.time, start.price],
              [end.time, end.price],
              [start.time, end.price],
              [start.time, start.price]
            ];

            console.log('🔲 Rectangle data:', rectangleData);

            const rectangleSeries = {
              name: `Drawing_${index}`,
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: rectangleData,
              lineStyle: {
                color: '#00e7b6',
                width: 2,
                type: 'solid'
              },
              symbol: 'none',
              animation: false,
              silent: false, // Allow clicking for delete functionality
              z: 1000,
              zlevel: 1,
              clip: false // Prevent clipping
            };

            console.log('🔲 Adding rectangle series to chart:', rectangleSeries);
            drawingSeries.push(rectangleSeries);
          } else {
            console.log('🔲 Rectangle drawing skipped - insufficient points:', drawing.points);
          }
          break;

        case 'measure':
          if (drawing.points && drawing.points.length >= 2) {
            const [start, end] = drawing.points;
            // Calculate distance and percentage
            const timeDiff = Math.abs(new Date(end.time).getTime() - new Date(start.time).getTime());
            const priceDiff = Math.abs(end.price - start.price);
            const priceChange = ((end.price - start.price) / start.price * 100).toFixed(2);

            drawingSeries.push({
              name: `Drawing_${index}`,
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: [[start.time, start.price], [end.time, end.price]],
              lineStyle: {
                color: '#ffa500',
                width: 2,
                type: 'dashed'
              },
              symbol: 'circle',
              symbolSize: 6,
              animation: false,
              silent: false, // Allow clicking for delete functionality
              z: 1000,
              zlevel: 1,
              clip: false,
              label: {
                show: true,
                position: 'middle',
                formatter: `${priceChange}%`,
                color: '#ffa500',
                fontSize: 12
              }
            });
          }
          break;




      }
    });

    // Add current drawing if in progress - always show preview
    if (currentDrawing && isDrawing) {
      switch (currentDrawing.type) {
        case 'line':
          if (currentDrawing.points && currentDrawing.points.length >= 1) {
            // Always show preview line, even with just one point
            let points = currentDrawing.points;

            // If we only have one point, create a very short line for visibility
            if (points.length === 1) {
              const singlePoint = points[0];
              // Create a tiny offset for the second point to make it visible
              points = [singlePoint, singlePoint];
            }

            drawingSeries.push({
              name: 'CurrentDrawing',
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: points.map((point: any) => [point.time, point.price]),
              lineStyle: {
                color: '#00e7b6',
                width: 3,
                type: 'dashed',
                opacity: 0.9
              },
              symbol: 'circle',
              symbolSize: 6,
              animation: false,
              silent: true,
              z: 1001,
              zlevel: 1,
              clip: false // Prevent clipping for current drawing
            });
          }
          break;

        case 'circle':
          if (currentDrawing.points && currentDrawing.points.length >= 2) {
            const [center, radiusPoint] = currentDrawing.points;
            const centerTime = new Date(center.time).getTime();
            const radiusTime = new Date(radiusPoint.time).getTime();
            const timeDiff = Math.abs(radiusTime - centerTime);
            const priceDiff = Math.abs(radiusPoint.price - center.price);

            // Create circle preview (simplified as octagon)
            const circlePoints = [];
            for (let i = 0; i <= 16; i++) {
              const angle = (i / 16) * 2 * Math.PI;
              const timeOffset = Math.cos(angle) * timeDiff;
              const priceOffset = Math.sin(angle) * priceDiff;
              const pointTime = new Date(centerTime + timeOffset).toISOString();
              const pointPrice = center.price + priceOffset;
              circlePoints.push([pointTime, pointPrice]);
            }

            drawingSeries.push({
              name: 'CurrentDrawing',
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: circlePoints,
              lineStyle: {
                color: '#00e7b6',
                width: 2,
                type: 'dashed',
                opacity: 0.8
              },
              symbol: 'none',
              animation: false,
              silent: true,
              z: 1001,
              zlevel: 1,
              clip: false
            });
          }
          break;

        case 'rectangle':
          if (currentDrawing.points && currentDrawing.points.length >= 2) {
            const [start, end] = currentDrawing.points;
            drawingSeries.push({
              name: 'CurrentDrawing',
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: [
                [start.time, start.price],
                [end.time, start.price],
                [end.time, end.price],
                [start.time, end.price],
                [start.time, start.price]
              ],
              lineStyle: {
                color: '#00e7b6',
                width: 2,
                type: 'dashed',
                opacity: 0.8
              },
              symbol: 'none',
              animation: false,
              silent: true,
              z: 1001,
              zlevel: 1,
              clip: false // Prevent clipping for rectangle preview
            });
          }
          break;

        case 'measure':
          // Show simple line preview for measure tool
          if (currentDrawing.points && currentDrawing.points.length >= 2) {
            const [start, end] = currentDrawing.points;
            drawingSeries.push({
              name: 'CurrentDrawing',
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: [[start.time, start.price], [end.time, end.price]],
              lineStyle: {
                color: '#ffa500',
                width: 2,
                type: 'dashed',
                opacity: 0.8
              },
              symbol: 'circle',
              symbolSize: 4,
              animation: false,
              silent: true,
              z: 1001,
              zlevel: 1,
              clip: false
            });
          }
          break;
      }

      // Add start point marker for better visibility (for all tools)
      if (currentDrawing.points && currentDrawing.points.length >= 1) {
        drawingSeries.push({
          name: 'StartPoint',
          type: 'scatter',
          coordinateSystem: 'cartesian2d',
          data: [[currentDrawing.points[0].time, currentDrawing.points[0].price]],
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#00e7b6',
            borderColor: '#ffffff',
            borderWidth: 2,
            shadowColor: '#00e7b6',
            shadowBlur: 10
          },
          animation: false,
          silent: true,
          z: 1002,
          zlevel: 1,
          clip: false // Prevent clipping for start point
        });
      }
    }

    return drawingSeries;
  };

  // Memoized chart options to prevent unnecessary regeneration when only tool state changes
  const chartOptions = useMemo(() => {
    // Safety check to prevent crashes
    if (!stockData || stockData.length === 0) {
      return {
        backgroundColor: chartSettings.backgroundColor,
        series: []
      };
    }

    // Format data for ECharts
    const formattedData = stockData.map(item => [
      // Convert timestamp to date string if needed
      typeof item.time === 'number' ? new Date(item.time * 1000).toISOString() : item.time,
      item.open,
      item.close,
      item.low,
      item.high,
      item.volume || 0
    ]);

    return {
      backgroundColor: chartSettings.backgroundColor,
      // Disable animations during drawing mode to prevent candlestick compression
      animation: chartSettings.enableAnimations && !isDrawingMode && !isDrawing,
      legend: {
        show: false,
      },
      tooltip: {
        trigger: 'axis',
        // Always show tooltip for cursor movement, but reduce functionality when drawing
        show: chartSettings.showCrosshair,
        axisPointer: {
          type: chartSettings.showCrosshair ? 'cross' : 'none',
          lineStyle: {
            color: 'rgba(0, 231, 182, 0.2)',
            width: 1
          }
        },
        backgroundColor: 'rgba(0, 0, 0, 0.95)',
        borderColor: 'rgba(0, 231, 182, 0.3)',
        borderWidth: 1,
        borderRadius: 12,
        padding: [12, 16],
        shadowBlur: 20,
        shadowColor: 'rgba(0, 0, 0, 0.8)',
        shadowOffsetX: 0,
        shadowOffsetY: 4,
        textStyle: {
          color: chartSettings.textColor,
          fontFamily: "'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif",
          fontSize: 13,
          fontWeight: 500,
        },
        // Allow tooltip positioning but reduce conflicts during active drawing
        position: (isDrawing && currentDrawing) ? 'none' : undefined,
        formatter: (params: any) => {
          if (!params || !params[0]) return '';

          const axisValue = params[0].axisValue; // This is the time from x-axis
          const candleData = params[0].data; // This is the OHLC data [open, close, low, high]

          // Find the data index to get volume from stockData
          const dataIndex = params[0].dataIndex;
          const volume = stockData[dataIndex]?.volume || 'N/A';

          // Parse the time properly
          const date = new Date(axisValue);

          // Format time based on active timeframe (same as x-axis)
          let timeStr = '';
          switch (activeTimeframe) {
            case '1m':
            case '5m':
            case '15m':
              // For minute charts, always show full date and time in tooltip for clarity
              const month = (date.getMonth() + 1).toString().padStart(2, '0');
              const day = date.getDate().toString().padStart(2, '0');
              const year = date.getFullYear();
              const hours = date.getHours().toString().padStart(2, '0');
              const minutes = date.getMinutes().toString().padStart(2, '0');
              timeStr = `${month}/${day}/${year} ${hours}:${minutes}`;
              break;
            case '1h':
            case '4h':
              // Use UTC to avoid timezone issues with hourly data
              const utcHour = date.getUTCHours().toString().padStart(2, '0');
              const utcMonth = (date.getUTCMonth() + 1).toString().padStart(2, '0');
              const utcDay = date.getUTCDate().toString().padStart(2, '0');
              const utcYear = date.getUTCFullYear();
              timeStr = `${utcMonth}/${utcDay}/${utcYear} ${utcHour}:00`;
              break;
            case '1d':
              timeStr = `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;
              break;
            case '1M':
              timeStr = `${date.getMonth() + 1}/${date.getFullYear()}`;
              break;
            case '1Y':
              timeStr = date.getFullYear().toString();
              break;
            default:
              timeStr = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
          }

          // Format volume with commas for readability
          const formattedVolume = typeof volume === 'number' ? volume.toLocaleString() : volume;

          if (!candleData || candleData.length < 4) return '';

          // ECharts candlestick data format is [open, close, low, high]
          const [open, close, low, high] = candleData;
          const change = close - open;
          const changePercent = ((change / open) * 100).toFixed(2);
          const changeColor = change >= 0 ? '#00e7b6' : '#ff4757';
          const glowColor = change >= 0 ? 'rgba(0, 231, 182, 0.3)' : 'rgba(255, 71, 87, 0.3)';

          return `
            <div style="
              background: linear-gradient(135deg, rgba(0, 0, 0, 0.98) 0%, rgba(0, 10, 20, 0.95) 100%);
              border-radius: 8px;
              padding: 12px;
              font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
              box-shadow: 0 8px 24px rgba(0, 0, 0, 0.8);
              backdrop-filter: blur(15px);
              min-width: 180px;
            ">
              <div style="color: #00e7b6; font-size: 12px; font-weight: 600; margin-bottom: 8px;">${currentSymbol} • ${timeStr}</div>

              <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                <span style="color: rgba(255, 255, 255, 0.7); font-size: 11px;">Open</span>
                <span style="color: #ffffff; font-size: 11px; font-weight: 500;">$${open.toFixed(4)}</span>
              </div>

              <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                <span style="color: rgba(255, 255, 255, 0.7); font-size: 11px;">High</span>
                <span style="color: #00e7b6; font-size: 11px; font-weight: 500;">$${high.toFixed(4)}</span>
              </div>

              <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                <span style="color: rgba(255, 255, 255, 0.7); font-size: 11px;">Low</span>
                <span style="color: #ff4757; font-size: 11px; font-weight: 500;">$${low.toFixed(4)}</span>
              </div>

              <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                <span style="color: rgba(255, 255, 255, 0.7); font-size: 11px;">Close</span>
                <span style="color: ${changeColor}; font-size: 11px; font-weight: 500;">$${close.toFixed(4)}</span>
              </div>

              <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
                <span style="color: rgba(255, 255, 255, 0.7); font-size: 11px;">Change</span>
                <div style="
                  background: ${change >= 0 ? 'linear-gradient(135deg, rgba(0, 231, 182, 0.2), rgba(0, 231, 182, 0.08))' : 'linear-gradient(135deg, rgba(255, 71, 87, 0.2), rgba(255, 71, 87, 0.08))'};
                  padding: 3px 8px;
                  border-radius: 6px;
                  text-align: right;
                ">
                  <div style="color: ${changeColor}; font-weight: 700; font-size: 11px;">
                    ${change >= 0 ? '+' : ''}$${change.toFixed(4)}
                  </div>
                  <div style="color: ${changeColor}; font-size: 9px; font-weight: 600; opacity: 0.9;">
                    ${change >= 0 ? '+' : ''}${changePercent}%
                  </div>
                </div>
              </div>

              <div style="display: flex; justify-content: space-between;">
                <span style="color: rgba(255, 255, 255, 0.5); font-size: 10px;">Volume</span>
                <span style="color: rgba(255, 255, 255, 0.8); font-size: 10px; font-weight: 500;">${formattedVolume}</span>
              </div>
            </div>
          `;
        }
      },
      axisPointer: {
        link: { xAxisIndex: 'all' },
        label: {
          backgroundColor: '#777',
          formatter: (params: any) => {
            const date = new Date(params.value);

            // Use the same formatting logic as x-axis labels
            switch (activeTimeframe) {
              case '1m':
              case '5m':
              case '15m':
                // For minute charts, show date and time in axis pointer
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');
                return `${month}/${day}\n${hours}:${minutes}`;
              case '1h':
              case '4h':
                // Use UTC to avoid timezone issues with hourly data
                const utcHour = date.getUTCHours().toString().padStart(2, '0');
                const utcMonth = (date.getUTCMonth() + 1).toString().padStart(2, '0');
                const utcDay = date.getUTCDate().toString().padStart(2, '0');
                return `${utcHour}:00\n${utcMonth}/${utcDay}`;
              case '1d':
                return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear().toString().slice(-2)}`;
              case '1M':
                return `${date.getMonth() + 1}/${date.getFullYear().toString().slice(-2)}`;
              case '1Y':
                return date.getFullYear().toString();
              default:
                return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
            }
          }
        }
      },
      grid: [
        // Main price chart grid
        {
          left: '3%',
          right: '5%',
          top: '8%',
          height: (() => {
            let height = 85;
            if (chartSettings.showVolume) height -= 15;
            return `${height}%`;
          })(),
          borderColor: chartSettings.gridColor,
        },
        // Volume grid
        ...(chartSettings.showVolume ? [{
          left: '3%',
          right: '5%',
          top: '85%',
          height: '12%',
          borderColor: chartSettings.gridColor,
        }] : [])
      ],
      xAxis: [
        // Main chart x-axis
        {
          type: 'category',
          data: stockData.map(item => {
            try {
              if (typeof item.time === 'number') {
                const date = new Date(item.time * 1000);
                if (isNaN(date.getTime())) {
                  console.warn('Invalid timestamp:', item.time);
                  return new Date().toISOString(); // fallback to current time
                }
                return date.toISOString();
              }
              return item.time;
            } catch (error) {
              console.error('Error processing time data:', error, item);
              return new Date().toISOString(); // fallback to current time
            }
          }),
          scale: true,
          boundaryGap: false,
          axisLine: { lineStyle: { color: chartSettings.gridColor } },
          axisLabel: {
            color: chartSettings.textColor,
            fontFamily: "'Roboto Mono', monospace",
            fontSize: 10,
            // Show more labels for smaller timeframes when zoomed out
            interval: (() => {
              const currentZoom = chartRef.current?.getOption()?.dataZoom?.[0];
              const zoomSpan = currentZoom ? (currentZoom.end - currentZoom.start) : 30;

              switch (activeTimeframe) {
                case '1m':
                case '5m':
                case '15m':
                  // Show more labels when zoomed out to see date changes better
                  if (zoomSpan > 80) return Math.floor(stockData.length / 20); // Many labels when very zoomed out
                  if (zoomSpan > 50) return Math.floor(stockData.length / 12); // More labels when zoomed out
                  return Math.floor(stockData.length / 8); // Fewer labels when zoomed in
                case '1h':
                case '4h':
                  return Math.floor(stockData.length / 10);
                default:
                  return 'auto';
              }
            })(),
            formatter: (value: string) => {
              const date = new Date(value);

              // Check if date is valid
              if (isNaN(date.getTime())) {
                console.warn('Invalid date value in formatter:', value);
                return 'Invalid Date';
              }

              // Removed debug logging to prevent errors

              // Format based on active timeframe for better readability
              try {
                switch (activeTimeframe) {
                  case '1m':
                  case '5m':
                  case '15m':
                    // For minute charts, show time and date when zoomed out, just time when zoomed in
                    const currentZoom = chartRef.current?.getOption()?.dataZoom?.[0];
                    const zoomSpan = currentZoom ? (currentZoom.end - currentZoom.start) : 30;

                    if (zoomSpan > 50) {
                      // When zoomed out (showing more than 50% of data), show date and time
                      const month = (date.getMonth() + 1).toString().padStart(2, '0');
                      const day = date.getDate().toString().padStart(2, '0');
                      const hours = date.getHours().toString().padStart(2, '0');
                      const minutes = date.getMinutes().toString().padStart(2, '0');
                      return `${month}/${day}\n${hours}:${minutes}`;
                    } else {
                      // When zoomed in, just show time
                      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                    }
                  case '1h':
                  case '4h':
                    // Show hour and date for hourly charts - use UTC to avoid timezone issues
                    const utcHour = date.getUTCHours().toString().padStart(2, '0');
                    const utcMonth = (date.getUTCMonth() + 1).toString().padStart(2, '0');
                    const utcDay = date.getUTCDate().toString().padStart(2, '0');
                    return `${utcHour}:00\n${utcMonth}/${utcDay}`;
                  case '1d':
                    // Show month/day/year for daily charts to make years clear
                    return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear().toString().slice(-2)}`;
                  case '1M':
                    // Show month/year for monthly charts
                    return `${date.getMonth() + 1}/${date.getFullYear().toString().slice(-2)}`;
                  case '1Y':
                    // Show year for yearly charts
                    return date.getFullYear().toString();
                  default:
                    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                }
              } catch (error) {
                console.error('Error formatting date:', error, 'Date:', date, 'Value:', value);
                return 'Error';
              }
            }
          },
          splitLine: {
            show: chartSettings.showGrid,
            lineStyle: {
              color: chartSettings.gridColor,
              type: 'dashed'
            }
          },
          min: 'dataMin',
          max: 'dataMax',
          // Ensure the rightmost position shows the most recent data
          realtime: true
        },
        // Volume x-axis
        ...(chartSettings.showVolume ? [{
          type: 'category',
          gridIndex: 1,
          data: stockData.map(item => {
            try {
              if (typeof item.time === 'number') {
                const date = new Date(item.time * 1000);
                if (isNaN(date.getTime())) {
                  console.warn('Invalid timestamp:', item.time);
                  return new Date().toISOString(); // fallback to current time
                }
                return date.toISOString();
              }
              return item.time;
            } catch (error) {
              console.error('Error processing time data:', error, item);
              return new Date().toISOString(); // fallback to current time
            }
          }),
          scale: true,
          boundaryGap: false,
          axisLine: { lineStyle: { color: chartSettings.gridColor } },
          axisLabel: { show: false },
          axisTick: { show: false },
          splitLine: { show: false },
          min: 'dataMin',
          max: 'dataMax',
          realtime: true
        }] : [])
      ],
      yAxis: [
        // Main price y-axis
        {
          scale: true, // Always use scaling for better zoom behavior
          type: chartSettings.priceScaleMode === 'logarithmic' ? 'log' : 'value',
          splitNumber: 6,
          position: 'right',
          // Don't set min/max here - let auto-scaling handle it completely
          // This prevents conflicts with the autoScaleYAxis function
          axisLine: { lineStyle: { color: chartSettings.gridColor } },
          axisLabel: {
            color: chartSettings.textColor,
            fontFamily: "'Roboto Mono', monospace",
            fontSize: 10,
            inside: false,
            formatter: (value: number) => {
              // Format price labels with appropriate precision
              if (value >= 1000) {
                return value.toFixed(0);
              } else if (value >= 100) {
                return value.toFixed(1);
              } else if (value >= 10) {
                return value.toFixed(2);
              } else {
                return value.toFixed(3);
              }
            }
          },
          splitLine: {
            show: chartSettings.showGrid,
            lineStyle: {
              color: chartSettings.gridColor,
              type: 'dashed'
            }
          },
          // Add day boundary markers for minute timeframes
          ...((['1m', '5m', '15m'].includes(activeTimeframe)) ? {
            splitLine: {
              show: chartSettings.showGrid,
              lineStyle: {
                color: (value: any, index: number) => {
                  // Highlight day boundaries with a slightly different color
                  const date = new Date(value);
                  const isStartOfDay = date.getHours() === 0 && date.getMinutes() === 0;
                  return isStartOfDay ? 'rgba(255, 255, 255, 0.3)' : chartSettings.gridColor;
                },
                type: 'dashed',
                width: (value: any) => {
                  const date = new Date(value);
                  const isStartOfDay = date.getHours() === 0 && date.getMinutes() === 0;
                  return isStartOfDay ? 2 : 1; // Thicker lines for day boundaries
                }
              }
            }
          } : {})
        },
        // Volume y-axis
        ...(chartSettings.showVolume ? [{
          scale: true,
          gridIndex: 1,
          position: 'right',
          splitNumber: 2,
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false }
        }] : [])
      ],
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0, 1],
          // Use current zoom level if available, otherwise use timeframe-based default
          start: (() => {
            // Try to get current zoom level from chart to maintain user's zoom preference
            const currentZoom = chartRef.current?.getOption()?.dataZoom?.[0];
            if (currentZoom && typeof currentZoom.start === 'number') {
              return currentZoom.start;
            }

            // Default timeframe-based zoom for initial load
            switch (activeTimeframe) {
              case '1m':
              case '5m':
              case '15m':
                return 90; // Show last 10% for minute timeframes (more zoomed in)
              case '1h':
                return 85; // Show last 15% for hourly
              case '4h':
              case '1d':
              case '1month':
              case '1year':
                return 80; // Show last 20% for longer timeframes
              default:
                return 85;
            }
          })(),
          end: 100,  // Always end at the most recent data point
          minSpan: 0.1, // Minimum zoom level (0.1% of data) - allows zooming out to show 5 years
          maxSpan: 100, // Maximum zoom level (100% of data)
          zoomOnMouseWheel: true,
          moveOnMouseMove: true,
          preventDefaultMouseMove: false,
          // Improved zoom handling with automatic y-axis scaling
          onDataZoom: (params: any) => {

            // INSTANT auto-scale y-axis - no delays for immediate response
            // Skip auto-scaling during drawing to prevent chart movement
            if (!drawingLocked && !isDrawing && !recentlyUnlocked) {
              autoScaleYAxis(params.start, params.end);
            } else {
              console.log('❌ DataZoom auto-scaling skipped: chart locked for drawing');
            }

            // Re-render drawings after zoom with proper coordinate synchronization
            const reRenderDrawings = () => {
              if (chartRef.current && drawings.length > 0) {
                // Simply re-render the chart with current drawings
                // The drawings already have both absolute and relative coordinates
                // so they should maintain their correct positions
                const newOption = getChartOptions();

                // Only update the series (drawings), not the Y-axis to preserve auto-scaling
                chartRef.current.setOption({
                  series: newOption.series
                }, {
                  notMerge: false, // Merge to preserve other settings including Y-axis
                  lazyUpdate: false,
                  silent: true // Silent to prevent conflicts
                });

                // Re-apply auto-scaling immediately after drawings update
                // Skip auto-scaling during drawing to prevent chart movement
                if (!drawingLocked && !isDrawing && !recentlyUnlocked) {
                  autoScaleYAxis(params.start, params.end);
                } else {
                  console.log('❌ Post-drawing auto-scaling skipped: chart locked for drawing');
                }
              }
            };

            // Immediate re-render for instant response
            if (drawings.length > 0) {
              reRenderDrawings();
            }

            // DISABLE auto-reset when drawing tools are active to prevent unwanted zooming
            // Auto-reset only when zoomed extremely far out (beyond 5 years) AND not using drawing tools
            if (params.end - params.start > 99.9 && !isDrawingMode && !activeTool && !isDrawing && !drawingLocked) {
              setTimeout(() => {
                if (chartRef.current && !isDrawingMode && !activeTool && !isDrawing && !drawingLocked) {
                  chartRef.current.dispatchAction({
                    type: 'dataZoom',
                    start: 0, // Allow showing full range when auto-resetting
                    end: 100
                  });
                }
              }, 200);
            }

            // Refresh labels for minute timeframes (y-axis scaling already handled above)
            if (['1m', '5m', '15m'].includes(activeTimeframe)) {
              setTimeout(() => {
                if (chartRef.current) {
                  const newOption = getChartOptions();
                  // Only update x-axis labels, not the entire chart to preserve Y-axis auto-scaling
                  chartRef.current.setOption({
                    xAxis: newOption.xAxis
                  }, {
                    notMerge: false,
                    lazyUpdate: false,
                    silent: true
                  });
                }
              }, 150); // Longer delay to ensure auto-scaling completes first
            }
          }
        }
      ],
      series: [
        // Main price series - conditional based on chart type
        ...(chartSettings.chartType === 'candle' || chartSettings.chartType === 'hollow_candle' ? [{
          name: 'Candle',
          type: 'candlestick',
          data: formattedData.map(item => item.slice(1, 5)), // [open, close, low, high]
          // Enhanced candlestick sizing for better zoom visibility and drawing compatibility
          barWidth: '60%', // Responsive width that adjusts to zoom
          barMaxWidth: 20, // Maximum width to prevent oversized candles
          barMinHeight: 5, // Increased minimum height to prevent compression
          large: true, // Enable large dataset optimization
          progressive: 0, // Disable progressive rendering to prevent conflicts
          // Disable animations during drawing mode to prevent compression
          animation: !isDrawingMode && !isDrawing,
          animationDuration: 0, // Instant updates during drawing
          itemStyle: {
            // Bullish candles (close >= open)
            color: chartSettings.chartType === 'hollow_candle' ? 'transparent' : chartSettings.bullishCandleColor,
            // Bearish candles (close < open)
            color0: chartSettings.chartType === 'hollow_candle' ? 'transparent' : chartSettings.bearishCandleColor,
            // Border colors for both bullish and bearish
            borderColor: chartSettings.bullishCandleColor,
            borderColor0: chartSettings.bearishCandleColor,
            borderWidth: chartSettings.chartType === 'hollow_candle' ? 2 : 1
          },
          emphasis: {
            itemStyle: {
              color: chartSettings.chartType === 'hollow_candle' ? 'transparent' : chartSettings.bullishCandleColor,
              color0: chartSettings.chartType === 'hollow_candle' ? 'transparent' : chartSettings.bearishCandleColor,
              borderColor: chartSettings.bullishCandleColor,
              borderColor0: chartSettings.bearishCandleColor,
              borderWidth: chartSettings.chartType === 'hollow_candle' ? 3 : 2
            }
          },
          // Ensure candlesticks maintain proper z-index below drawings
          z: 1,
          zlevel: 0
        }] : []),

        // Line chart
        ...(chartSettings.chartType === 'line' ? [{
          name: 'Price',
          type: 'line',
          data: formattedData.map((item) => [
            item[0], // time (already formatted)
            item[2]  // close price
          ]),
          lineStyle: {
            color: chartSettings.bullishCandleColor,
            width: 2
          },
          symbol: 'none',
          smooth: false
        }] : []),

        // Area chart
        ...(chartSettings.chartType === 'area' ? [{
          name: 'Price',
          type: 'line',
          data: formattedData.map((item) => [
            item[0], // time (already formatted)
            item[2]  // close price
          ]),
          lineStyle: {
            color: chartSettings.bullishCandleColor,
            width: 2
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: chartSettings.bullishCandleColor + '80' },
                { offset: 1, color: chartSettings.bullishCandleColor + '10' }
              ]
            }
          },
          symbol: 'none',
          smooth: true
        }] : []),



        // Volume
        ...(chartSettings.showVolume ? [{
          name: 'Volume',
          type: 'bar',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: formattedData.map((item) => {
            // item format: [time, open, close, low, high, volume]
            const open = item[1];
            const close = item[2];
            const color = close >= open ? chartSettings.bullishCandleColor : chartSettings.bearishCandleColor;
            return {
              value: item[5], // volume
              itemStyle: {
                color: color,
                opacity: 0.5
              }
            };
          })
        }] : []),



        // Add drawing series
        ...createDrawingSeries()
      ]
    };
  }, [
    stockData,
    chartSettings,
    activeTimeframe,
    drawings,
    currentDrawing,
    // Only include drawing states that actually affect visual rendering
    isDrawing ? currentDrawing : null, // Only when actively drawing
    drawingLocked,
    recentlyUnlocked,
    activeTool === 'delete' ? 'delete' : null // Only delete mode affects rendering
  ]); // Only regenerate when these essential values change

  // Legacy function for compatibility with existing code
  const getChartOptions = () => chartOptions;

  return (
    <div className="trading-chart-container">
      {/* Top toolbar */}
      <div className="chart-toolbar">
        <div className="toolbar-left">
          {/* Clean scanner search */}
          <div className="futuristic-scanner-search">
            <div className="scanner-input-wrapper">
              <input
                type="text"
                className="scanner-input"
                placeholder="Search symbols..."
                value={currentSymbol}
                onChange={(e) => setCurrentSymbol(e.target.value.toUpperCase())}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    const input = e.currentTarget.value.trim().toUpperCase();
                    const symbolRegex = /^[A-Z]{1,5}$/;
                    if (symbolRegex.test(input)) {
                      setCurrentSymbol(input);
                    }
                  }
                }}
              />
              {isLoading && <FaSpinner className="scanner-loading" />}
            </div>
            <div className="scanner-status">
              <span className="status-indicator active"></span>
              <span className="status-text">LIVE SCAN</span>
            </div>
          </div>

          {/* Timeframe selector with comprehensive options */}
          <div className="timeframe-selector">
            <button
              className={activeTimeframe === '1m' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('1m');
                fetchData('1m');
              }}
              title="1-minute candles (7 days max)"
            >
              1m
            </button>
            <button
              className={activeTimeframe === '5m' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('5m');
                fetchData('5m');
              }}
              title="5-minute candles (7 days max)"
            >
              5m
            </button>
            <button
              className={activeTimeframe === '15m' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('15m');
                fetchData('15m');
              }}
              title="15-minute candles (7 days max)"
            >
              15m
            </button>
            <button
              className={activeTimeframe === '1h' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('1h');
                fetchData('1h');
              }}
            >
              1h
            </button>
            <button
              className={activeTimeframe === '4h' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('4h');
                fetchData('4h');
              }}
            >
              4h
            </button>
            <button
              className={activeTimeframe === '1d' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('1d');
                fetchData('1d');
              }}
            >
              1D
            </button>
            <button
              className={activeTimeframe === '1M' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('1M');
                fetchData('1M');
              }}
            >
              1M
            </button>
            <button
              className={activeTimeframe === '1Y' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('1Y');
                fetchData('1Y');
              }}
            >
              1Y
            </button>
          </div>
        </div>

        {/* Chart controls - settings only */}
        <div className="chart-controls">
          <button
            className="chart-button"
            onClick={() => setShowSettingsModal(true)}
          >
            <FaCog />
            <span>Settings</span>
          </button>
        </div>
      </div>

      {/* Chart container */}
      <div className="chart-content">
        {/* TradingView-style Tools Sidebar */}
        <div className="drawing-tools-sidebar">
          <div className="drawing-tools-content">
            {/* All tools in a single clean grid */}
            <div className="tool-grid">
              {drawingTools.map(tool => (
                <button
                  key={tool.id}
                  className={`tool-button ${activeTool === tool.id ? 'active' : ''}`}
                  onClick={() => handleToolSelect(tool.id)}
                  title={tool.name}
                >
                  {tool.iconType === 'image' ? (
                    <img src={tool.icon} alt={tool.name} className="tool-icon-image" />
                  ) : (
                    <span className="tool-icon-text">{tool.icon}</span>
                  )}
                </button>
              ))}

              {/* Help/Shortcuts Button - no gap */}
              <button
                className={`tool-button ${showShortcuts ? 'active' : ''}`}
                onClick={() => setShowShortcuts(!showShortcuts)}
                title="Keyboard Shortcuts"
              >
                <span className="tool-icon-text">?</span>
              </button>
            </div>

              {/* Keyboard Shortcuts Panel */}
              {showShortcuts && (
                <div className="shortcuts-panel">
                  <div className="shortcuts-header">Shortcuts</div>
                  <div className="shortcuts-list">
                    <div className="shortcut-item">
                      <span className="shortcut-key">Esc</span>
                      <span className="shortcut-desc">Cancel</span>
                    </div>

                    <div className="shortcut-item">
                      <span className="shortcut-key">L</span>
                      <span className="shortcut-desc">Line</span>
                    </div>
                    <div className="shortcut-item">
                      <span className="shortcut-key">C</span>
                      <span className="shortcut-desc">Circle</span>
                    </div>
                    <div className="shortcut-item">
                      <span className="shortcut-key">R</span>
                      <span className="shortcut-desc">Rectangle</span>
                    </div>
                    <div className="shortcut-item">
                      <span className="shortcut-key">M</span>
                      <span className="shortcut-desc">Measure</span>
                    </div>

                    <div className="shortcut-item">
                      <span className="shortcut-key">D</span>
                      <span className="shortcut-desc">Delete</span>
                    </div>
                    <div className="shortcut-item">
                      <span className="shortcut-key">Ctrl+Del</span>
                      <span className="shortcut-desc">Clear All</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

        <div className="chart-area">
          <div className="chart-header">
            <div className="symbol-info">
              <span className="symbol">
                {lastValidSymbol !== currentSymbol ? lastValidSymbol : currentSymbol}
              </span>
              <div className="volume-info">
                <span className="volume-label">Vol</span>
                <span className="volume-value">{(priceInfo.volume / 1000000).toFixed(2)}M</span>
                <span className={`volume-change ${priceInfo.close > priceInfo.open ? 'positive' : 'negative'}`}>
                  {((priceInfo.close - priceInfo.open) / priceInfo.open * 100).toFixed(2)}%
                </span>
              </div>
            </div>

            {/* Drawing Tool Status */}
            {(isDrawingMode || activeTool === 'delete') && activeTool && (
              <div className="drawing-status">
                <span className="drawing-status-text">
                  {activeTool === 'delete' ? 'Delete mode: Click on any drawing to delete it' :
                   isDrawing ? 'Drawing' : 'Click to start'}: {activeTool !== 'delete' ? drawingTools.find(tool => tool.id === activeTool)?.name : ''}
                  {isDrawing && (activeTool === 'line' || activeTool === 'rectangle') && (
                    <span className="drawing-instruction"> - Click again to finish</span>
                  )}
                </span>
                <button
                  className="cancel-drawing"
                  onClick={() => {
                    console.log('🚫 Canceling drawing operation - ISOLATED from chart regeneration');
                    // Simple state reset - optimized dependencies prevent unnecessary chart regeneration
                    setActiveTool(null);
                    setIsDrawingMode(false);
                    setCurrentDrawing(null);
                    setIsDrawing(false);
                    setDrawingLocked(false);
                    setRecentlyUnlocked(false);
                    console.log('✅ Drawing canceled - No chart regeneration triggered');
                  }}
                >
                  Cancel
                </button>
              </div>
            )}
          </div>

          {error && <div className="error-message">{error}</div>}

          <div
            className={`chart-container ${isDrawingMode ? 'drawing-mode' : ''} ${activeTool === 'delete' ? 'delete-mode' : ''}`}
            onClick={activeTool === 'delete' ? undefined : handleContainerClick}
            onDoubleClick={() => {
              // Double-click escape hatch - force reset if chart is stuck or cursor is frozen
              console.log('🔄 Double-click escape hatch triggered - resetting chart states');
              resetDrawingStates('double-click escape hatch', true); // Preserve zoom

              // Additional recovery for cursor freezing issues
              setTimeout(() => {
                if (chartRef.current) {
                  try {
                    chartRef.current.resize();
                    console.log('✅ Double-click recovery completed');
                  } catch (error) {
                    console.error('Double-click recovery failed:', error);
                  }
                }
              }, 100);
            }}
            onMouseMove={(event) => {
              // Always track mouse movement in drawing mode for continuous line preview
              if (isDrawingMode && activeTool && chartRef.current) {
                const rect = event.currentTarget.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;

                try {
                  // Try ECharts coordinate conversion first
                  const pointInPixel = [x, y];
                  const pointInGrid = chartRef.current.convertFromPixel('grid', pointInPixel);

                  let point = null;

                  if (pointInGrid && pointInGrid.length >= 2) {
                    const [timeIndex, price] = pointInGrid;
                    const clampedTimeIndex = Math.max(0, Math.min(Math.floor(timeIndex), stockData.length - 1));
                    const timeValue = stockData[clampedTimeIndex]?.time || stockData[0]?.time;
                    const actualTime = typeof timeValue === 'number' ? new Date(timeValue * 1000).toISOString() : timeValue;

                    point = {
                      time: actualTime,
                      price: price
                    };
                  } else {
                    // Fallback calculation when ECharts conversion fails
                    const chartContainer = event.currentTarget.querySelector('.echarts-for-react');
                    if (chartContainer && stockData.length > 0) {
                      const chartRect = chartContainer.getBoundingClientRect();
                      const containerRect = event.currentTarget.getBoundingClientRect();

                      // Calculate relative position within the chart
                      const relativeX = (x - (chartRect.left - containerRect.left)) / chartRect.width;
                      const relativeY = (y - (chartRect.top - containerRect.top)) / chartRect.height;

                      // Account for chart margins (approximately 3% left, 5% right, 8% top, 17% bottom)
                      const adjustedX = Math.max(0, Math.min(1, (relativeX - 0.03) / (1 - 0.03 - 0.05)));
                      const adjustedY = Math.max(0, Math.min(1, (relativeY - 0.08) / (1 - 0.08 - 0.17)));

                      const dataIndex = Math.floor(adjustedX * stockData.length);
                      const prices = stockData.map(d => [d.low, d.high]).flat();
                      const minPrice = Math.min(...prices);
                      const maxPrice = Math.max(...prices);
                      const price = maxPrice - (adjustedY * (maxPrice - minPrice));

                      const clampedDataIndex = Math.max(0, Math.min(dataIndex, stockData.length - 1));
                      const timeValue = stockData[clampedDataIndex]?.time || stockData[0]?.time;
                      const actualTime = typeof timeValue === 'number' ? new Date(timeValue * 1000).toISOString() : timeValue;

                      point = {
                        time: actualTime,
                        price: Math.max(minPrice, Math.min(price, maxPrice))
                      };
                    }
                  }

                  // Update current drawing preview if we have a valid point
                  if (point && isDrawing && currentDrawing) {
                    switch (activeTool) {
                      case 'line':
                        setCurrentDrawing(prev => {
                          if (!prev || !prev.points || !prev.points[0]) return prev;
                          return {
                            ...prev,
                            points: [prev.points[0], point]
                          };
                        });
                        break;
                      case 'circle':
                        setCurrentDrawing(prev => {
                          if (!prev || !prev.points || !prev.points[0]) return prev;
                          return {
                            ...prev,
                            points: [prev.points[0], point]
                          };
                        });
                        break;
                      case 'rectangle':
                        setCurrentDrawing(prev => {
                          if (!prev || !prev.points || !prev.points[0]) return prev;
                          return {
                            ...prev,
                            points: [prev.points[0], point]
                          };
                        });
                        break;
                      case 'measure':
                        setCurrentDrawing(prev => {
                          if (!prev || !prev.points || !prev.points[0]) return prev;
                          return {
                            ...prev,
                            points: [prev.points[0], point]
                          };
                        });
                        break;
                    }
                  }
                } catch (error) {
                  // Silently handle coordinate conversion errors
                }
              }
            }}
          >
            <ReactECharts
              key={`${chartSettings.bullishCandleColor}-${chartSettings.bearishCandleColor}`}
              ref={(echarts) => {
                if (echarts) {
                  chartRef.current = echarts.getEchartsInstance();
                  // Chart instance is ready - no need to reset anything for tool selection
                }
              }}
              option={chartOptions}
              style={{ height: '650px', width: '100%' }}
              opts={{ renderer: 'canvas' }}
              onEvents={{
                'click': (params: any, echarts: any) => {
                  console.log('📊 ECharts click event fired:', {
                    activeTool,
                    isDrawingMode,
                    params: params
                  });

                  if ((isDrawingMode && activeTool) || activeTool === 'delete') {
                    handleChartClick(params, echarts);
                  } else {
                    console.log('❌ ECharts click ignored - conditions not met');
                  }
                },
                'mouseover': (params: any) => {
                  if (activeTool === 'delete' && params.seriesName && params.seriesName.startsWith('Drawing_')) {
                    console.log('Hovering over drawing:', params.seriesName);
                  }
                },
                'mousemove': (params: any, echarts: any) => {
                  // Only track mouse movement when actively drawing to prevent tooltip conflicts
                  if (isDrawingMode && activeTool && isDrawing) {
                    handleChartMouseMove(params, echarts);
                  }
                },
                'globalout': () => {
                  // Clear current drawing preview when mouse leaves chart
                  if (isDrawing && currentDrawing && currentDrawing.points && currentDrawing.points.length === 1) {
                    setCurrentDrawing(prev => {
                      if (!prev || !prev.points || !prev.points[0]) return prev;
                      return {
                        ...prev,
                        points: [prev.points[0]]
                      };
                    });
                  }
                }
              }}
            />
          </div>
        </div>
      </div>



      {/* Chart Settings Modal */}
      <ChartSettingsModal
        isOpen={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
        settings={chartSettings}
        onSettingsChange={(newSettings) => {
          console.log('Chart settings being updated:', newSettings);

          // CURSOR FREEZING FIX: Reset drawing states once to prevent cursor getting stuck
          // This was the main cause of cursor freezing after settings changes
          resetDrawingStates('chart settings changed', true);

          // Update settings state
          setChartSettings(newSettings);

          // Save settings to localStorage
          try {
            localStorage.setItem('tradingChartSettings', JSON.stringify(newSettings));
          } catch (error) {
            console.error('Error saving settings to localStorage:', error);
          }

          // Check if chart type is changing - this needs special handling
          const isChartTypeChanging = chartSettings.chartType !== newSettings.chartType;

          // Use a single timeout for chart update to prevent conflicts
          setTimeout(() => {
            if (chartRef.current) {
              try {
                const newOption = getChartOptions();

                // For chart type changes, we need a more complete update
                // but still preserve event handlers by not remounting the component
                chartRef.current.setOption(newOption, {
                  notMerge: isChartTypeChanging, // Force rebuild series for chart type changes
                  lazyUpdate: false,
                  silent: true // Silent to prevent animation conflicts
                });

                // Ensure chart is responsive after settings change
                chartRef.current.resize();

                // Additional recovery step for chart type changes
                if (isChartTypeChanging) {
                  setTimeout(() => {
                    if (chartRef.current) {
                      // Ensure the chart is fully responsive after type change
                      chartRef.current.resize();
                      console.log('✅ Chart type change completed successfully');
                    }
                  }, 100);
                }

                console.log('✅ Chart settings applied successfully');
              } catch (error) {
                console.error('Error applying chart settings:', error);
                // Fallback: try a simple state reset if chart update fails
                resetDrawingStates('chart settings update failed', true);
              }
            }
          }, 150); // Single timeout with reasonable delay

          console.log('Chart settings updated:', newSettings);
        }}
      />
    </div>
  );
};

export default TradingChart;
