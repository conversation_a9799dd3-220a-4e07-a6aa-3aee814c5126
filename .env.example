# Supabase configuration
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# Note: Never commit the service role key to your repository
# The service role key should only be used in secure server environments
# SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Encryption key for secure data transmission
VITE_ENCRYPTION_KEY=your-encryption-key-here

# Stripe Configuration (for Marketplace)
# ONLY the publishable key goes here - it's safe for client-side use
# NOTE: Marketplace now uses LIVE Stripe keys for production
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key_here

# NEVER put secret keys in .env files!
# Secret keys should ONLY be set in Supabase Edge Functions environment:
# - STRIPE_SECRET_KEY (set via: supabase secrets set STRIPE_SECRET_KEY=sk_live_...)
# - STRIPE_WEBHOOK_SECRET (set via: supabase secrets set STRIPE_WEBHOOK_SECRET=whsec_...)

# Growi Affiliate Tracking
VITE_GROWI_PUBLIC_ID=your-growi-public-id-here
