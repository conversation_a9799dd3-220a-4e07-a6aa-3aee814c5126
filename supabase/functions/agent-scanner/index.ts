import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { SP500_STOCKS } from "../aura/data/sp500-stocks.ts"
import { RUSSELL2000_STOCKS } from "../aura/data/russell2000-stocks.ts"
import { NASDAQ_STOCKS } from "../aura/data/nasdaq-stocks.ts"
import { NASDAQ100_STOCKS } from "../aura/data/nasdaq100-stocks.ts"
import { ALL_STOCKS } from "../aura/data/all-stocks.ts"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};



interface ScanRequest {
  agentId: string;
  marketIndex: string;
  userId: string;
}

interface ScanResult {
  symbol: string;
  signal: string;
  confidence: number;
  price: number;
  change: number;
  percentChange: number;
  isEnhanced?: boolean;
  riskManagement?: any;
  advancedAnalysis?: any;
  signalQuality?: any;
}

// Helper function to call agent-runner edge function
async function runAgentOnStock(
  supabase: any,
  agentId: string,
  symbol: string,
  timeframe: string = 'day'
): Promise<any> {
  try {
    const { data, error } = await supabase.functions.invoke('agent-runner', {
      body: {
        agentId: agentId,
        symbol: symbol,
        timeframe: timeframe
      }
    });

    if (error) {
      console.error(`Error running agent on ${symbol}:`, error);
      return null;
    }

    return data;
  } catch (error) {
    console.error(`Exception running agent on ${symbol}:`, error);

    // Log specific error types for debugging
    if (error instanceof Error) {
      if (error.message.includes('indicator') || error.message.includes('calculation')) {
        console.error(`Indicator calculation error for ${symbol}:`, error.message);
      } else if (error.message.includes('data') || error.message.includes('historical')) {
        console.error(`Data error for ${symbol}:`, error.message);
      }
    }

    return null;
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { agentId, marketIndex, userId }: ScanRequest = await req.json();

    // Validate input
    if (!agentId || !marketIndex || !userId) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get the agent configuration
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('*')
      .eq('id', agentId)
      .eq('user_id', userId)
      .single();

    if (agentError || !agent) {
      return new Response(
        JSON.stringify({ error: 'Agent not found' }),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    console.log(`Agent found: ${agent.name}`);
    console.log(`Agent configuration:`, {
      hasConfiguration: !!agent.configuration,
      blockCount: agent.configuration?.blocks?.length || 0,
      entryBlockId: agent.configuration?.entryBlockId,
      blockTypes: agent.configuration?.blocks?.map(b => b.type) || []
    });

    // Get stock list based on market index
    let stockList: string[] = [];
    switch (marketIndex) {
      case 'sp500':
        stockList = SP500_STOCKS;
        break;
      case 'nasdaq':
        stockList = NASDAQ_STOCKS;
        break;
      case 'nasdaq100':
        stockList = NASDAQ100_STOCKS;
        break;
      case 'russell2000':
        stockList = RUSSELL2000_STOCKS;
        break;
      case 'all':
        stockList = ALL_STOCKS;
        break;
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid market index' }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
    }

    console.log(`Scanning ${stockList.length} stocks with agent: ${agent.name}`);

    // Test the agent with a single stock first to validate it works
    console.log(`Testing agent with AAPL first...`);
    try {
      const testResult = await runAgentOnStock(supabase, agentId, 'AAPL');
      if (testResult) {
        console.log(`Test result for AAPL:`, {
          signal: testResult?.signal,
          confidence: testResult?.confidence,
          hasResult: !!testResult
        });
      } else {
        console.warn(`Test failed for AAPL - agent may have issues`);
      }
    } catch (error) {
      console.error(`Test failed for AAPL:`, error);
    }

    const results: ScanResult[] = [];
    const batchSize = 10; // Process stocks in efficient batches
    let totalProcessed = 0;
    let totalBullish = 0;
    let totalBearish = 0;
    let totalNeutral = 0;
    let totalErrors = 0;

    // Process all stocks in the selected market index
    const scanLimit = stockList.length;
    console.log(`Processing ${scanLimit} stocks in batches of ${batchSize}`);

    // Process stocks in batches
    for (let i = 0; i < scanLimit; i += batchSize) {
      const batch = stockList.slice(i, i + batchSize);
      console.log(`Processing batch ${Math.floor(i / batchSize) + 1}: ${batch.join(', ')}`);

      const batchPromises = batch.map(async (symbol) => {
        try {
          totalProcessed++;

          // Use agent-runner edge function instead of duplicating logic
          console.log(`Running agent on ${symbol}...`);
          const agentResult = await runAgentOnStock(supabase, agentId, symbol);

          if (!agentResult) {
            console.log(`❌ ${symbol} - No result from agent-runner`);
            totalErrors++;
            return null;
          }

          // Count all signals for debugging
          if (agentResult?.signal === 'bullish') {
            totalBullish++;
          } else if (agentResult?.signal === 'bearish') {
            totalBearish++;
          } else if (agentResult?.signal === 'neutral') {
            totalNeutral++;
          }

          // Check if this is an enhanced signal output
          const isEnhancedSignal = agentResult?.risk_management || agentResult?.advanced_analysis || agentResult?.signal_quality;

          console.log(`${symbol}: ${agentResult.signal} (${agentResult.confidence}%)${isEnhancedSignal ? ' [Enhanced]' : ''}`);

          // Apply signal quality filters if available
          let passesQualityFilter = true;
          if (isEnhancedSignal && agentResult.signal_quality) {
            passesQualityFilter = agentResult.signal_quality.overall_score >= 60; // Minimum quality threshold
            console.log(`Signal quality score: ${agentResult.signal_quality.overall_score}% (passes: ${passesQualityFilter})`);
          }

          // Filter for bullish signals with confidence > 50%
          if (agentResult && agentResult.signal === 'bullish' && agentResult.confidence > 50 && passesQualityFilter) {
            console.log(`✅ ${symbol} PASSED filter - adding to results`);

            // Extract price information from agent result (agent-runner now provides this)
            const currentPrice = agentResult.price?.current || 0;
            const change = agentResult.price?.change || 0;
            const percentChange = agentResult.price?.percentChange || 0;



            const result: any = {
              symbol: symbol,
              signal: agentResult.signal,
              confidence: agentResult.confidence,
              price: currentPrice,
              change: change,
              percentChange: percentChange,
              isEnhanced: isEnhancedSignal
            };

            // Add enhanced signal information if available
            if (isEnhancedSignal) {
              if (agentResult.risk_management) {
                result.riskManagement = {
                  stopLoss: agentResult.risk_management.stop_loss?.price,
                  stopLossPercentage: agentResult.risk_management.stop_loss?.percentage,
                  takeProfit: agentResult.risk_management.take_profit?.targets?.[0]?.price,
                  riskRewardRatio: agentResult.risk_management.risk_reward_ratio,
                  positionSizePercentage: agentResult.risk_management.position_size_suggestion?.percentage_of_portfolio
                };
              }

              if (agentResult.advanced_analysis) {
                result.advancedAnalysis = {
                  trendStrength: agentResult.advanced_analysis.trend_strength?.score,
                  volatilityLevel: agentResult.advanced_analysis.volatility_assessment?.level,
                  momentumDirection: agentResult.advanced_analysis.momentum_analysis?.momentum_direction,
                  correlationScore: agentResult.advanced_analysis.correlation_analysis?.market_correlation
                };
              }

              if (agentResult.signal_quality) {
                result.signalQuality = {
                  overallScore: agentResult.signal_quality.overall_score,
                  volumeConfirmation: agentResult.signal_quality.volume_confirmation,
                  trendAlignment: agentResult.signal_quality.trend_alignment,
                  qualityFactors: agentResult.signal_quality.quality_factors
                };
              }
            }

            console.log(`Result object for ${symbol}:`, result);
            return result;
          } else {
            const failReason = !agentResult ? 'no result' :
                              agentResult.signal !== 'bullish' ? 'not bullish' :
                              agentResult.confidence <= 50 ? 'low confidence' :
                              !passesQualityFilter ? 'failed quality filter' : 'unknown';
            console.log(`❌ ${symbol} FAILED filter - not adding to results (${failReason})`);
          }

          return null;
        } catch (error) {
          totalErrors++;
          console.error(`Error processing ${symbol}:`, error);
          return null;
        }
      });

      const batchResults = await Promise.all(batchPromises);
      console.log(`Batch ${Math.floor(i / batchSize) + 1} completed:`, {
        totalPromises: batchPromises.length,
        totalResults: batchResults.length,
        nullResults: batchResults.filter(r => r === null).length,
        validResults: batchResults.filter(r => r !== null).length
      });

      const validResults = batchResults.filter(result => result !== null) as ScanResult[];
      console.log(`Valid results from batch:`, validResults.map(r => `${r.symbol}: ${r.signal} (${r.confidence}%)`));
      results.push(...validResults);
      console.log(`Total results so far: ${results.length}`);

      // Add a small delay between batches to avoid overwhelming the API
      if (i + batchSize < scanLimit) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    // Sort results by confidence (highest first)
    results.sort((a, b) => b.confidence - a.confidence);

    // Return all results (no artificial limit)
    const limitedResults = results;

    console.log(`Scanning Summary:
      - Total Processed: ${totalProcessed}
      - Total Bullish: ${totalBullish}
      - Total Bearish: ${totalBearish}
      - Total Neutral: ${totalNeutral}
      - Total Errors: ${totalErrors}
      - Final Results: ${limitedResults.length}
    `);

    return new Response(
      JSON.stringify({
        results: limitedResults,
        stocksScanned: totalProcessed, // Add stocksScanned for time tracking
        totalScanned: totalProcessed,
        marketIndex: marketIndex,
        agentName: agent.name
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Agent scanner error:', error);
    console.error('Error stack:', error.stack);
    console.error('Error message:', error.message);

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        details: error.message,
        stack: error.stack
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
